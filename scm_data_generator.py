import torch
import pandas as pd
import random
import os
import numpy as np
import networkx as nx
from datetime import datetime
from scm_model import SCM, SkipDatasetException
from utils_scm import draw_graph, uniform_int_sampler_f, uniform_sampler_f

try:
    import igraph as ig
except ImportError:
    raise ImportError("需要安装igraph库才能生成随机DAG。请使用 pip install python-igraph 安装。")


class SeedManager:
    """统一的种子管理系统，简化种子分配逻辑"""
    
    # 种子类型偏移量配置
    SEED_TYPE_OFFSETS = {
        'dag_generation': 100000,
        'scm_model': 200000,
        'config_generation': 300000,
        'data_sampling': 400000
    }
    
    @classmethod
    def get_dataset_seed(cls, dataset_index, seed_type, seed=None, seeds_config=None):
        """
        根据种子系统配置获取指定数据集和种子类型的种子
        
        参数:
            dataset_index: 数据集索引(从0开始)
            seed_type: 种子类型
            seed: 全局种子
            seeds_config: 种子配置字典
            
        返回:
            该数据集该种子类型应使用的种子值，如果为None则表示使用随机种子
        """
        if seed_type not in cls.SEED_TYPE_OFFSETS:
            raise ValueError(f"不支持的种子类型: {seed_type}")
        
        type_offset = cls.SEED_TYPE_OFFSETS[seed_type]
        
        # 情况1: 完全随机
        if seed is None and seeds_config is None:
            return None
        
        # 情况2: 仅有种子配置
        if seed is None and seeds_config is not None:
            return cls._handle_seeds_config_only(dataset_index, seed_type, seeds_config, type_offset)
        
        # 情况3: 仅有全局种子
        if seed is not None and seeds_config is None:
            return seed + type_offset + dataset_index
        
        # 情况4: 同时有全局种子和种子配置
        if seed is not None and seeds_config is not None:
            return cls._handle_both_seed_and_config(dataset_index, seed_type, seed, seeds_config, type_offset)
        
        return None
    
    @classmethod
    def _handle_seeds_config_only(cls, dataset_index, seed_type, seeds_config, type_offset):
        """处理仅有种子配置的情况"""
        if seed_type in seeds_config and seeds_config[seed_type] is not None:
            seed_list = seeds_config[seed_type]
            if dataset_index < len(seed_list):
                return seed_list[dataset_index]
            else:
                # 种子列表长度不足，基于固定种子生成递增种子
                base_seed = 1000000 + type_offset
                extra_index = dataset_index - len(seed_list)
                return base_seed + extra_index
        return None
    
    @classmethod
    def _handle_both_seed_and_config(cls, dataset_index, seed_type, seed, seeds_config, type_offset):
        """处理同时有全局种子和种子配置的情况"""
        if seed_type in seeds_config and seeds_config[seed_type] is not None:
            seed_list = seeds_config[seed_type]
            if dataset_index < len(seed_list):
                return seed_list[dataset_index]
            else:
                # 种子列表长度不足，基于全局种子生成递增种子
                extra_index = dataset_index - len(seed_list)
                return seed + type_offset + extra_index
        else:
            # 未提供种子列表，使用全局种子+偏移量
            return seed + type_offset


class DAGStructure:
    """DAG结构基类，定义通用的DAG创建接口"""
    
    def __init__(self, predefined_intervention_nodes=None, predefined_perturbation_nodes=None, 
                 predefined_unobserved_nodes=None):
        self.predefined_intervention_nodes = predefined_intervention_nodes
        self.predefined_perturbation_nodes = predefined_perturbation_nodes
        self.predefined_unobserved_nodes = predefined_unobserved_nodes
    
    def create_dag(self):
        """创建DAG，返回 (G, root_nodes, layer_nodes, target_node, feature_nodes, dag_info)"""
        raise NotImplementedError
    
    def _create_dag_info(self, structure_type, dag_type='predefined'):
        """创建DAG信息字典"""
        return {
            'type': dag_type,
            'structure_type': structure_type,
            'predefined_intervention_nodes': self.predefined_intervention_nodes,
            'predefined_perturbation_nodes': self.predefined_perturbation_nodes,
            'predefined_unobserved_nodes': self.predefined_unobserved_nodes
        }


class PredefinedDAGStructure(DAGStructure):
    """预定义DAG结构类"""
    
    # 预定义结构配置：支持自由给定具体的图结构
    STRUCTURE_CONFIGS = {
        'common_cause': {
            'nodes': ['U', 'X1', 'X2', 'X3', 'Y'],
            'edges': [('U', 'X1'), ('U', 'X2'), ('U', 'X3'), ('X1', 'Y'), ('X2', 'Y'), ('X3', 'Y')],
            'root_nodes': ['U'],
            'layer_nodes': [['U'], ['X1', 'X2', 'X3'], ['Y']],
            'target_node': 'Y',
            'feature_nodes': ['U', 'X1', 'X2', 'X3']
        },
        'chain': {
            'nodes': ['X1', 'X2', 'X3', 'Y'],
            'edges': [('X1', 'X2'), ('X2', 'X3'), ('X3', 'Y')],
            'root_nodes': ['X1'],
            'layer_nodes': [['X1'], ['X2'], ['X3'], ['Y']],
            'target_node': 'Y',
            'feature_nodes': ['X1', 'X2', 'X3']
        },
        'fork': {
            'nodes': ['Z', 'X1', 'X2', 'Y'],
            'edges': [('Z', 'X1'), ('Z', 'X2'), ('Z', 'Y')],
            'root_nodes': ['Z'],
            'layer_nodes': [['Z'], ['X1', 'X2', 'Y']],
            'target_node': 'Y',
            'feature_nodes': ['X1', 'X2']
        },
        'collider': {
            'nodes': ['X1', 'X2', 'Y'],
            'edges': [('X1', 'Y'), ('X2', 'Y')],
            'root_nodes': ['X1', 'X2'],
            'layer_nodes': [['X1', 'X2'], ['Y']],
            'target_node': 'Y',
            'feature_nodes': ['X1', 'X2']
        },
        'diamond': {
            'nodes': ['X', 'Z1', 'Z2', 'Y'],
            'edges': [('X', 'Z1'), ('X', 'Z2'), ('Z1', 'Y'), ('Z2', 'Y')],
            'root_nodes': ['X'],
            'layer_nodes': [['X'], ['Z1', 'Z2'], ['Y']],
            'target_node': 'Y',
            'feature_nodes': ['X', 'Z1', 'Z2']
        },
        'simple': {
            'nodes': ['U', 'X', 'Y'],
            'edges': [('U', 'Y'), ('U', 'X')],
            'root_nodes': ['U'],
            'layer_nodes': [['U'], ['X', 'Y']],
            'target_node': 'Y',
            'feature_nodes': ['U', 'X']
        },
        'complex': {
            'nodes': ['A', 'B', 'C', 'D', 'E', 'F', 'Y'],
            'edges': [('A', 'B'), ('A', 'C'), ('B', 'D'), ('B', 'E'), ('C', 'E'), ('C', 'F'), ('D', 'Y'), ('E', 'Y'), ('F', 'Y')],
            'root_nodes': ['A'],
            'layer_nodes': [['A'], ['B', 'C'], ['D', 'E', 'F'], ['Y']],
            'target_node': 'Y',
            'feature_nodes': ['A', 'B', 'C', 'D', 'E', 'F']
        }
    }
    
    def __init__(self, structure_type, **kwargs):
        super().__init__(**kwargs)
        self.structure_type = structure_type
        if structure_type not in self.STRUCTURE_CONFIGS:
            raise ValueError(f"未知的结构类型: {structure_type}")
    
    def create_dag(self):
        """创建预定义结构的DAG"""
        config = self.STRUCTURE_CONFIGS[self.structure_type]
        
        G = nx.DiGraph()
        G.add_nodes_from(config['nodes'])
        G.add_edges_from(config['edges'])
        
        dag_info = self._create_dag_info(self.structure_type)
        
        return (G, config['root_nodes'], config['layer_nodes'], 
                config['target_node'], config['feature_nodes'], dag_info)


class RandomDAGStructure(DAGStructure):
    """随机DAG结构类：ER、SF"""
    
    # 节点数范围定义
    NODE_SIZE_RANGES = {
        'small': (11, 20), 
        'medium': (21, 50), 
        'large': (51, 100), 
        'vlarge': (101, 200)
    }
    
    def __init__(self, structure_type, size='small', seed=None, remove_isolated_nodes=False, 
                 avg_in_degree=(1.2, 1.7), **kwargs):
        super().__init__(**kwargs)
        self.structure_type = structure_type
        self.size = size
        self.seed = seed
        self.remove_isolated_nodes = remove_isolated_nodes
        self.avg_in_degree = avg_in_degree
        
        # 从structure_type中提取图类型
        self.graph_type = 'ER' if structure_type == 'random_ER' else 'SF'
    
    def create_dag(self):
        """创建随机DAG"""
        # 设置随机种子
        if self.seed is not None:
            random.seed(self.seed)
            np.random.seed(self.seed)
        
        # 确定节点数量
        if self.size in self.NODE_SIZE_RANGES:
            min_nodes, max_nodes = self.NODE_SIZE_RANGES[self.size]
            d = random.randint(min_nodes, max_nodes)
        else:
            d = random.randint(11, 20)  # 默认使用small规模
        
        # 确定边数量
        avg_degree = random.uniform(self.avg_in_degree[0], self.avg_in_degree[1])
        s0 = int(d * avg_degree)
        
        # 生成随机DAG
        adj_matrix = self._simulate_dag(d, s0, self.graph_type, self.remove_isolated_nodes)
        
        # 创建图
        G = nx.DiGraph()
        actual_size = adj_matrix.shape[0]
        nodes = [f'X{i}' for i in range(actual_size)]
        G.add_nodes_from(nodes)
        
        # 添加边
        for i in range(actual_size):
            for j in range(actual_size):
                if adj_matrix[i, j] == 1:
                    G.add_edge(nodes[i], nodes[j])
        
        # 找出根节点
        root_nodes = [node for node in G.nodes if G.in_degree(node) == 0]
        
        # 拓扑排序确定层次
        layer_nodes = self._create_layer_structure(G)
        
        # 创建DAG信息
        dag_info = self._create_dag_info(self.structure_type, 'random')
        dag_info.update({
            'graph_type': self.graph_type,
            'size': self.size,
            'node_count': actual_size
        })
        
        # 对于随机图类型，返回None作为target_node和feature_nodes
        return (G, root_nodes, layer_nodes, None, None, dag_info)
    
    def _simulate_dag(self, d, s0, graph_type, remove_isolated_nodes=False):
        """生成随机DAG的邻接矩阵"""
        def _random_permutation(M):
            P = np.random.permutation(np.eye(M.shape[0]))
            return P.T @ M @ P
        
        def _acyclic_orientation(B_und):
            return np.tril(B_und, k=-1)
        
        def _remove_isolating_node(B):
            non_iso_index = np.logical_or(B.any(axis=0), B.any(axis=1))
            if non_iso_index.any():
                return B[non_iso_index][:, non_iso_index]
            return B
        
        def _graph_to_adjmat(G):
            return np.array(G.get_adjacency().data)
        
        # 确保参数合理
        d = max(2, d)
        s0 = max(1, min(s0, d * (d - 1) // 2))
        
        try:
            if graph_type == 'ER':
                G_und = ig.Graph.Erdos_Renyi(n=d, m=s0)
            elif graph_type == 'SF':
                m = max(1, min(int(round(s0 / d)), d - 1))
                G_und = ig.Graph.Barabasi(n=d, m=m, directed=False)
            else:
                raise ValueError('未知的图类型')
        except Exception as e:
            print(f"生成图时出错: {e}")
            G_und = ig.Graph.Erdos_Renyi(n=min(d, 5), m=min(s0, 5))
        
        try:
            B_und = _graph_to_adjmat(G_und)
            B_und = _random_permutation(B_und)
            B = _acyclic_orientation(B_und)
            
            if B.size > 0 and remove_isolated_nodes:
                B = _remove_isolating_node(B)
            
            if B.size > 0 and B.shape[0] > 1:
                B_perm = _random_permutation(B).astype(int)
                if ig.Graph.Adjacency(B_perm.tolist()).is_dag():
                    return B_perm
                else:
                    return B.astype(int)
            else:
                return B.astype(int)
        except Exception as e:
            print(f"处理邻接矩阵时出错: {e}")
            simple_dag = np.zeros((min(d, 3), min(d, 3)), dtype=int)
            if simple_dag.shape[0] > 1:
                simple_dag[0, 1] = 1
            if simple_dag.shape[0] > 2:
                simple_dag[1, 2] = 1
            return simple_dag
    
    def _create_layer_structure(self, G):
        """根据拓扑排序创建层次结构"""
        try:
            topo_sorted = list(nx.topological_sort(G))
            layer_nodes = []
            remaining_nodes = set(topo_sorted)
            
            while remaining_nodes:
                current_layer = []
                for node in sorted(list(remaining_nodes)):
                    if all(pred not in remaining_nodes for pred in G.predecessors(node)):
                        current_layer.append(node)
                
                if not current_layer:
                    break
                
                current_layer.sort()
                layer_nodes.append(current_layer)
                remaining_nodes -= set(current_layer)
            
            if remaining_nodes:
                layer_nodes.append(list(remaining_nodes))
            
            return layer_nodes
        except nx.NetworkXUnfeasible:
            print("警告: 生成的图不是DAG，可能存在循环。")
            return 


class CustomEdgesDAGStructure(DAGStructure):
    """自定义边列表DAG结构类"""
    
    def __init__(self, custom_edges, **kwargs):
        super().__init__(**kwargs)
        self.custom_edges = custom_edges
    
    def create_dag(self):
        """根据自定义边列表创建DAG"""
        G = nx.DiGraph()
        
        # 从边列表中提取所有唯一节点
        all_nodes = set()
        for parent, child in self.custom_edges:
            all_nodes.add(parent)
            all_nodes.add(child)
        
        G.add_nodes_from(all_nodes)
        G.add_edges_from(self.custom_edges)
        
        # 找出根节点
        root_nodes = [node for node in G.nodes if G.in_degree(node) == 0]
        
        # 推断层次结构
        layer_nodes = self._infer_layer_structure(G, root_nodes)
        
        # 默认将最后一层的第一个节点作为目标节点
        target_node = layer_nodes[-1][0] if layer_nodes[-1] else None
        feature_nodes = [node for node in G.nodes if node != target_node]
        
        dag_info = self._create_dag_info('custom_edges')
        
        return G, root_nodes, layer_nodes, target_node, feature_nodes, dag_info
    
    def _infer_layer_structure(self, G, root_nodes):
        """推断层次结构"""
        layer_nodes = [list(root_nodes)]
        remaining_nodes = set(G.nodes) - set(root_nodes)
        
        while remaining_nodes:
            current_layer = []
            for node in remaining_nodes:
                parents = set(G.predecessors(node))
                if all(parent in [n for layer in layer_nodes for n in layer] for parent in parents):
                    current_layer.append(node)
            
            if not current_layer:
                print("警告: 图中可能存在循环依赖，无法完全分层。将剩余节点放入最后一层。")
                layer_nodes.append(list(remaining_nodes))
                break
            
            layer_nodes.append(current_layer)
            remaining_nodes -= set(current_layer)
        
        return layer_nodes


class DAGFactory:
    """DAG工厂类，统一创建不同类型的DAG"""

    @staticmethod
    def create_dag(structure_type=None, custom_edges=None, size='small', seed=None,
                   remove_isolated_nodes=False, predefined_intervention_nodes=None,
                   predefined_perturbation_nodes=None, predefined_unobserved_nodes=None,
                   avg_in_degree=(1.2, 1.7)):
        """
        统一的DAG创建接口

        参数:
            structure_type: 结构类型
            custom_edges: 自定义边列表
            size: 随机图规模
            seed: 随机种子
            remove_isolated_nodes: 是否移除孤立节点
            predefined_intervention_nodes: 预定义干预节点
            predefined_perturbation_nodes: 预定义扰动节点
            predefined_unobserved_nodes: 预定义不可观测节点
            avg_in_degree: 平均入度范围

        返回:
            (G, root_nodes, layer_nodes, target_node, feature_nodes, dag_info)
        """
        common_kwargs = {
            'predefined_intervention_nodes': predefined_intervention_nodes,
            'predefined_perturbation_nodes': predefined_perturbation_nodes,
            'predefined_unobserved_nodes': predefined_unobserved_nodes
        }

        # 优先使用自定义边列表
        if custom_edges is not None:
            dag_structure = CustomEdgesDAGStructure(custom_edges, **common_kwargs)
            return dag_structure.create_dag()

        # 检查是否为随机图类型
        if structure_type and structure_type.startswith('random'):
            dag_structure = RandomDAGStructure(
                structure_type=structure_type,
                size=size,
                seed=seed,
                remove_isolated_nodes=remove_isolated_nodes,
                avg_in_degree=avg_in_degree,
                **common_kwargs
            )
            return dag_structure.create_dag()

        # 预定义结构
        if structure_type:
            dag_structure = PredefinedDAGStructure(structure_type, **common_kwargs)
            return dag_structure.create_dag()

        raise ValueError("必须指定structure_type或custom_edges")


class DatasetInfo:
    """数据集信息管理类"""

    @staticmethod
    def create_data_info(dataset_name, config, custom_dag_type, custom_dag_size,
                        scm, task_type):
        """创建数据集信息字典"""
        data_info = {
            'dataset_name': dataset_name,
            'config': config,
            'dag_type': custom_dag_type,
            'dag_size': custom_dag_size,
            'num_nodes': len(scm.dag.nodes()),
            'num_edges': len(scm.dag.edges()),
            'task_type': task_type
        }

        # 添加SNR信息（如果可用）
        if hasattr(scm, 'snr_info') and scm.snr_info is not None:
            data_info['snr_info'] = scm.snr_info
        if hasattr(scm, 'snr_validation_results') and scm.snr_validation_results is not None:
            data_info['snr_validation'] = scm.snr_validation_results

        return data_info


class DebugChecker:
    """调试检查工具类"""

    @staticmethod
    def check_feature_count_mismatch(dataset, kwargs):
        """检查特征数量是否匹配"""
        custom_dag_size = kwargs.get('custom_dag_size')
        if custom_dag_size is None:
            return

        # 节点数范围定义
        nodenumclass = {'small': (11, 20), 'medium': (21, 50), 'large': (51, 100), 'vlarge': (101, 200)}

        if custom_dag_size not in nodenumclass:
            return

        scm = dataset[-2]
        x_original = dataset[1]

        node_count = len(scm.dag.nodes())
        feature_count = x_original.shape[1]
        min_nodes, max_nodes = nodenumclass[custom_dag_size]
        expected_features = node_count - 1

        # 检查节点数是否在指定规模范围内
        if not (min_nodes <= node_count <= max_nodes):
            DebugChecker._handle_out_of_range(scm, custom_dag_size, node_count, feature_count)
        elif feature_count != expected_features:
            DebugChecker._handle_feature_mismatch(scm, custom_dag_size, node_count, feature_count, expected_features)

    @staticmethod
    def _handle_out_of_range(scm, custom_dag_size, node_count, feature_count):
        """处理节点数超出范围的情况"""
        print(f"节点数{node_count}不在指定的{custom_dag_size}规模范围内，特征数量是{feature_count}")
        DebugChecker._print_debug_info(scm)
        DebugChecker._save_debug_graph(scm, f"out_of_range_{custom_dag_size}_nodes{node_count}_features{feature_count}")
        input("Press Enter to continue...")

    @staticmethod
    def _handle_feature_mismatch(scm, custom_dag_size, node_count, feature_count, expected_features):
        """处理特征数量不匹配的情况"""
        print(f"特征数量是{feature_count}，节点数是{node_count}，不匹配{custom_dag_size}类别的预期特征数{expected_features}")
        DebugChecker._print_debug_info(scm)

        # 检查哪些节点缺失
        all_nodes = set(scm.dag.nodes())
        target_node = scm.selected_target
        expected_feature_nodes = all_nodes - {target_node}
        actual_feature_nodes = set(scm.selected_features) if scm.selected_features else set()

        missing_nodes = expected_feature_nodes - actual_feature_nodes
        extra_nodes = actual_feature_nodes - expected_feature_nodes

        if missing_nodes:
            print(f"  缺失的特征节点: {missing_nodes}")
        if extra_nodes:
            print(f"  多余的特征节点: {extra_nodes}")

        DebugChecker._save_debug_graph(scm, f"mismatch_{custom_dag_size}_nodes{node_count}_features{feature_count}")
        input("Press Enter to continue...")

    @staticmethod
    def _print_debug_info(scm):
        """打印调试信息"""
        print(f"调试信息:")
        print(f"  目标节点: {scm.selected_target}")
        print(f"  特征节点数量: {len(scm.selected_features) if scm.selected_features else 0}")
        print(f"  drop_node_ratio: {scm.drop_node_ratio}")

    @staticmethod
    def _save_debug_graph(scm, filename_prefix):
        """保存调试图形"""
        debug_dir = "./debug_graphs"
        os.makedirs(debug_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
        graph_filename = f"{filename_prefix}_{timestamp}.png"
        graph_path = os.path.join(debug_dir, graph_filename)

        draw_graph(scm.dag, graph_path,
                  target_node=scm.selected_target,
                  selected_features=scm.selected_features,
                  assignment=getattr(scm, 'assignment', None),
                  scm=scm,
                  model_results=None)

        print(f"已保存调试DAG图到: {graph_path}")


class DatasetGenerator:
    """数据集生成器类，统一处理不同类型的数据生成"""

    def __init__(self, h_config, seed=None, seeds_config=None, allow_skip=True):
        self.h_config = h_config
        self.seed = seed
        self.seeds_config = seeds_config
        self.allow_skip = allow_skip

        # 统计信息
        self.total_attempts = 0
        self.successful_datasets = 0
        self.skipped_datasets = 0

    def generate_datasets(self, num_dataset, **kwargs):
        """
        生成多个数据集的主方法

        参数:
            num_dataset: 要生成的数据集数量
            **kwargs: 其他参数

        返回:
            数据集列表
        """
        datasets = []

        # 设置全局随机种子
        self._set_global_seed()

        print(f"开始生成 {num_dataset} 个数据集，跳过策略: {'允许跳过' if self.allow_skip else '不允许跳过'}")
        print(f"种子配置: seed={self.seed}, seeds_config={self.seeds_config}")

        # 循环生成直到达到目标数量
        while self.successful_datasets < num_dataset:
            self.total_attempts += 1
            current_attempt = self.total_attempts - 1

            # 获取各类型种子
            seeds = self._get_seeds_for_attempt(current_attempt)
            dataset_name = f'dataset_{self.successful_datasets}'

            print(f"\n尝试生成第 {self.successful_datasets + 1} 个数据集 (总尝试次数: {self.total_attempts})...")
            print(f"种子分配: DAG={seeds['dag']}, SCM={seeds['scm']}, Config={seeds['config']}, Data={seeds['data']}")

            try:
                # 生成单个数据集
                dataset = self._generate_single_dataset(dataset_name, seeds, **kwargs)
                datasets.append(dataset)

                self.successful_datasets += 1
                print(f"✅ 成功生成第 {self.successful_datasets} 个数据集")

                # 可选的调试检查
                self._optional_debug_check(dataset, kwargs)

            except SkipDatasetException as e:
                self._handle_skip_exception(e)
                continue
            except Exception as e:
                if not self._handle_general_exception(e):
                    raise
                continue

        self._print_statistics(num_dataset)
        return datasets

    def _set_global_seed(self):
        """设置全局随机种子"""
        if self.seed is not None:
            random.seed(self.seed)
            np.random.seed(self.seed)
            torch.manual_seed(self.seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed_all(self.seed)

    def _get_seeds_for_attempt(self, current_attempt):
        """获取当前尝试的各类型种子"""
        return {
            'dag': SeedManager.get_dataset_seed(current_attempt, 'dag_generation', self.seed, self.seeds_config),
            'scm': SeedManager.get_dataset_seed(current_attempt, 'scm_model', self.seed, self.seeds_config),
            'config': SeedManager.get_dataset_seed(current_attempt, 'config_generation', self.seed, self.seeds_config),
            'data': SeedManager.get_dataset_seed(current_attempt, 'data_sampling', self.seed, self.seeds_config)
        }

    def _generate_single_dataset(self, dataset_name, seeds, **kwargs):
        """生成单个数据集"""
        # 生成配置
        config = self._generate_config(seeds['config'])

        # 生成自定义DAG（如果需要）
        custom_dag = self._generate_custom_dag(seeds['dag'], kwargs)

        # 创建SCM模型
        scm = self._create_scm(config, custom_dag, seeds['scm'], kwargs)

        # 根据请求类型生成数据
        return self._generate_data_by_type(dataset_name, config, scm, kwargs)

    def _generate_config(self, config_seed):
        """生成SCM配置"""
        return config_generation(self.h_config, seed=config_seed)

    def _generate_custom_dag(self, dag_seed, kwargs):
        """生成自定义DAG（如果需要）"""
        custom_dag_type = kwargs.get('custom_dag_type')
        custom_edges = kwargs.get('custom_edges')

        if custom_dag_type is not None or custom_edges is not None:
            return DAGFactory.create_dag(
                structure_type=custom_dag_type,
                custom_edges=custom_edges,
                size=kwargs.get('custom_dag_size', 'small'),
                seed=dag_seed,
                remove_isolated_nodes=kwargs.get('remove_isolated_nodes', False),
                predefined_intervention_nodes=kwargs.get('predefined_intervention_nodes'),
                predefined_perturbation_nodes=kwargs.get('predefined_perturbation_nodes'),
                predefined_unobserved_nodes=kwargs.get('predefined_unobserved_nodes'),
                avg_in_degree=kwargs.get('avg_in_degree', (1.2, 1.7))
            )
        return None

    def _create_scm(self, config, custom_dag, scm_seed, kwargs):
        """创建SCM模型"""
        return SCM(
            config,
            custom_dag=custom_dag,
            seed=scm_seed,
            allow_skip=self.allow_skip,
            custom_functions=kwargs.get('custom_functions'),
            intervention_node_type=kwargs.get('intervention_node_type'),
            perturbation_node_type=kwargs.get('perturbation_node_type')
        )

    def _generate_data_by_type(self, dataset_name, config, scm, kwargs):
        """根据请求类型生成数据"""
        # 检查是否需要生成扰动数据
        if kwargs.get('perturbation_type') is not None:
            return self._generate_perturbation_dataset(dataset_name, config, scm, kwargs)

        # 检查是否需要生成干预数据
        elif kwargs.get('intervention_type') is not None:
            return self._generate_intervention_dataset(dataset_name, config, scm, kwargs)

        # 生成普通数据
        else:
            return self._generate_standard_dataset(dataset_name, config, scm, kwargs)

    def _generate_perturbation_dataset(self, dataset_name, config, scm, kwargs):
        """生成扰动数据集"""
        (x_original, y_original), (x_perturbation, y_perturbation) = scm.perturbation(
            perturbation_type=kwargs['perturbation_type'],
            perturbation_node_type=kwargs['perturbation_node_type'],
            perturbation_value_method=kwargs.get('perturbation_value_method', 'sample'),
            node_unobserved=kwargs.get('node_unobserved', False),
            unobserved_nodes=kwargs.get('unobserved_nodes'),
            sort_perturbation_nodes=True
        )

        data_info = DatasetInfo.create_data_info(
            dataset_name, config, kwargs.get('custom_dag_type'),
            kwargs.get('custom_dag_size'), scm, 'perturbation'
        )

        return [dataset_name, x_original, y_original, x_perturbation, y_perturbation, scm, data_info]

    def _generate_intervention_dataset(self, dataset_name, config, scm, kwargs):
        """生成干预数据集"""
        (x_original, y_original), (x_intervened, y_intervened) = scm.intervention(
            intervention_type=kwargs['intervention_type'],
            intervention_node_type=kwargs['intervention_node_type'],
            intervention_value_method=kwargs.get('intervention_value_method', 'sample'),
            node_unobserved=kwargs.get('node_unobserved', False),
            unobserved_nodes=kwargs.get('unobserved_nodes'),
            sort_intervention_nodes=True
        )

        data_info = DatasetInfo.create_data_info(
            dataset_name, config, kwargs.get('custom_dag_type'),
            kwargs.get('custom_dag_size'), scm, 'intervention'
        )

        return [dataset_name, x_original, y_original, x_intervened, y_intervened, scm, data_info]

    def _generate_standard_dataset(self, dataset_name, config, scm, kwargs):
        """生成标准数据集"""
        x, y, _ = scm.forward()

        data_info = DatasetInfo.create_data_info(
            dataset_name, config, kwargs.get('custom_dag_type'),
            kwargs.get('custom_dag_size'), scm, 'standard'
        )

        return [dataset_name, x, y, scm, data_info]

    def _optional_debug_check(self, dataset, kwargs):
        """可选的调试检查"""
        DebugChecker.check_feature_count_mismatch(dataset, kwargs)

    def _handle_skip_exception(self, e):
        """处理跳过异常"""
        self.skipped_datasets += 1
        print(f"⚠️  跳过第 {self.successful_datasets + 1} 个数据集的生成: {str(e)}")
        print(f"   原因: {str(e)}")

    def _handle_general_exception(self, e):
        """处理一般异常，返回是否应该继续"""
        print(f"❌ 生成第 {self.successful_datasets + 1} 个数据集时发生错误: {str(e)}")
        if self.allow_skip:
            self.skipped_datasets += 1
            print(f"   允许跳过，继续生成下一个数据集")
            return True
        else:
            print(f"   不允许跳过，停止生成")
            return False

    def _print_statistics(self, num_dataset):
        """打印统计信息"""
        print(f"\n" + "="*60)
        print(f"数据集生成完成!")
        print(f"目标数量: {num_dataset}")
        print(f"成功生成: {self.successful_datasets}")
        print(f"跳过数量: {self.skipped_datasets}")
        print(f"总尝试次数: {self.total_attempts}")
        print(f"成功率: {self.successful_datasets/self.total_attempts*100:.1f}%")
        print(f"="*60)


class DataSaver:
    """数据保存工具类，统一处理数据保存逻辑"""

    @staticmethod
    def save_datasets_to_csv(datasets, output_dir='./data', use_timestamp=True):
        """将生成的数据集保存为CSV文件"""
        if use_timestamp:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(output_dir, f"run_{timestamp}")
            print(f"数据将保存到: {output_dir}")

        os.makedirs(output_dir, exist_ok=True)

        for dataset in datasets:
            DataSaver._save_single_dataset(dataset, output_dir)

    @staticmethod
    def _save_single_dataset(dataset, output_dir):
        """保存单个数据集"""
        dataset_name = dataset[0]
        print(f"处理数据集: {dataset_name}")

        # 获取基本信息
        x = dataset[1]
        scm = dataset[-2]

        # 获取节点信息
        node_info = DataSaver._extract_node_info(scm)

        # 检查特征数量匹配
        mismatch = DataSaver._check_feature_mismatch(node_info, x)

        # 构建干预/扰动信息字符串
        intervention_info = DataSaver._build_intervention_info(node_info)

        # 创建列名
        feature_cols = DataSaver._create_feature_columns(node_info)
        target_col = DataSaver._create_target_column(node_info)

        # 保存特征数据
        DataSaver._save_feature_data(x, feature_cols, mismatch, node_info,
                                   output_dir, dataset_name, intervention_info)

        # 保存目标数据
        y = dataset[2]
        pd.DataFrame(y.cpu().numpy(), columns=target_col).to_csv(
            f"{output_dir}/{dataset_name}_y{intervention_info}.csv", index=False)

        # 保存修改后的数据（如果有）
        if len(dataset) > 4:
            DataSaver._save_modified_data(dataset, feature_cols, target_col,
                                        mismatch, node_info, output_dir,
                                        dataset_name, intervention_info)

        print(f"数据集 {dataset_name} 保存完成\n")

    @staticmethod
    def _extract_node_info(scm):
        """提取节点信息"""
        return {
            'target_node': getattr(scm, 'selected_target', None),
            'feature_nodes': getattr(scm, 'selected_features', []),
            'intervention_nodes': set(getattr(scm, 'intervention_nodes', [])),
            'perturbation_nodes': set(getattr(scm, 'perturbation_nodes', [])),
            'unobserved_nodes': set(getattr(scm, 'unobserved_nodes', []))
        }

    @staticmethod
    def _check_feature_mismatch(node_info, x):
        """检查特征数量是否匹配"""
        feature_nodes = node_info['feature_nodes']
        mismatch = len(feature_nodes) != x.shape[1]

        if mismatch:
            print(f"警告: 特征节点数量({len(feature_nodes)})与数据列数({x.shape[1]})不匹配!")
            print(f"特征节点列表: {feature_nodes}")
            print(f"这可能是因为存在不可观测节点: {node_info['unobserved_nodes']}")

        return mismatch

    @staticmethod
    def _build_intervention_info(node_info):
        """构建干预/扰动信息字符串"""
        intervention_nodes = node_info['intervention_nodes']
        perturbation_nodes = node_info['perturbation_nodes']

        if intervention_nodes:
            return "_intv" + "_".join(map(str, sorted(intervention_nodes)))
        elif perturbation_nodes:
            return "_ptb" + "_".join(map(str, sorted(perturbation_nodes)))
        return ""

    @staticmethod
    def _create_feature_columns(node_info):
        """创建特征列名"""
        feature_cols = []
        for node in node_info['feature_nodes']:
            col_name = f"feature{node}"

            # 添加干预/扰动标记
            if node in node_info['intervention_nodes']:
                col_name = f"feature{node}_intv"
            elif node in node_info['perturbation_nodes']:
                col_name = f"feature{node}_ptb"

            # 添加不可观测标记
            if node in node_info['unobserved_nodes']:
                col_name = f"{col_name}_unobserved"

            feature_cols.append(col_name)

        return feature_cols

    @staticmethod
    def _create_target_column(node_info):
        """创建目标列名"""
        target_node = node_info['target_node']
        col_name = f"target{target_node}"

        # 添加干预/扰动标记
        if target_node in node_info['intervention_nodes']:
            col_name = f"target{target_node}_intv"
        elif target_node in node_info['perturbation_nodes']:
            col_name = f"target{target_node}_ptb"

        # 添加不可观测标记
        if target_node in node_info['unobserved_nodes']:
            col_name = f"{col_name}_unobserved"

        return [col_name]

    @staticmethod
    def _save_feature_data(x, feature_cols, mismatch, node_info, output_dir, dataset_name, intervention_info):
        """保存特征数据"""
        if mismatch and node_info['unobserved_nodes'] and len(node_info['feature_nodes']) > x.shape[1]:
            # 处理不可观测节点的情况
            DataSaver._save_with_unobserved_nodes(x, feature_cols, node_info, output_dir, dataset_name, intervention_info)
        else:
            # 正常情况
            pd.DataFrame(x.cpu().numpy(), columns=feature_cols).to_csv(
                f"{output_dir}/{dataset_name}_x{intervention_info}.csv", index=False)

    @staticmethod
    def _save_with_unobserved_nodes(x, feature_cols, node_info, output_dir, dataset_name, intervention_info):
        """保存包含不可观测节点的数据"""
        observed_features = [feat for feat in node_info['feature_nodes'] if feat not in node_info['unobserved_nodes']]

        if len(observed_features) != x.shape[1]:
            print(f"警告: 即使排除不可观测节点后，特征数量({len(observed_features)})仍与数据列数({x.shape[1]})不匹配")

        # 创建全NaN的DataFrame
        df_full = pd.DataFrame(np.nan, index=range(len(x)), columns=feature_cols)

        # 填充实际数据
        x_np = x.cpu().numpy()
        for i, feat in enumerate(observed_features):
            if i < x.shape[1]:
                feat_idx = node_info['feature_nodes'].index(feat)
                df_full.iloc[:, feat_idx] = x_np[:, i]

        df_full.to_csv(f"{output_dir}/{dataset_name}_x{intervention_info}.csv", index=False)
        print(f"已创建包含不可观测节点(NaN值)的数据文件")

    @staticmethod
    def _save_modified_data(dataset, feature_cols, target_col, mismatch, node_info,
                          output_dir, dataset_name, intervention_info):
        """保存修改后的数据"""
        x_modified = dataset[3]
        y_modified = dataset[4]

        # 保存修改后的特征数据
        if mismatch and node_info['unobserved_nodes'] and len(node_info['feature_nodes']) > x_modified.shape[1]:
            DataSaver._save_modified_with_unobserved_nodes(x_modified, feature_cols, node_info,
                                                         output_dir, dataset_name, intervention_info)
        else:
            pd.DataFrame(x_modified.cpu().numpy(), columns=feature_cols).to_csv(
                f"{output_dir}/{dataset_name}_x_modified{intervention_info}.csv", index=False)

        # 保存修改后的目标数据
        pd.DataFrame(y_modified.cpu().numpy(), columns=target_col).to_csv(
            f"{output_dir}/{dataset_name}_y_modified{intervention_info}.csv", index=False)

    @staticmethod
    def _save_modified_with_unobserved_nodes(x_modified, feature_cols, node_info,
                                           output_dir, dataset_name, intervention_info):
        """保存包含不可观测节点的修改后数据"""
        observed_features = [feat for feat in node_info['feature_nodes'] if feat not in node_info['unobserved_nodes']]

        # 创建全NaN的DataFrame
        df_full_modified = pd.DataFrame(np.nan, index=range(len(x_modified)), columns=feature_cols)

        # 填充实际数据
        x_modified_np = x_modified.cpu().numpy()
        for i, feat in enumerate(observed_features):
            if i < x_modified.shape[1]:
                feat_idx = node_info['feature_nodes'].index(feat)
                df_full_modified.iloc[:, feat_idx] = x_modified_np[:, i]

        df_full_modified.to_csv(f"{output_dir}/{dataset_name}_x_modified{intervention_info}.csv", index=False)

# 主要的函数接口 
def config_generation(h_config, seed=None):
    """生成SCM的配置

    参数:
        h_config: 高级配置字典
        seed: 随机种子，用于确保配置生成的可重现性
    """
    # 初始化一个空列表，用于存储每层的节点数
    n_nodes_list = []
    # 创建一个节点数量采样器，从 h_config 中指定的最小和最大节点数之间均匀采样整数
    num_node_sampler = uniform_int_sampler_f(h_config['min_num_node'], h_config['max_num_node'], seed=seed)

    # 循环生成除最后一层外的每层节点数
    for layer_idx in range(h_config['num_layers'] - 1):
        n_nodes_list.append(num_node_sampler())

    # 根据 h_config 中的配置决定最后一层的节点数
    if h_config['single_effect_last_layer']:
        n_nodes_list.append(1)
    elif h_config['last_layer_fix_num_node']:
        n_nodes_list.append(h_config['num_node_last_layer'])
    else:
        n_nodes_list.append(num_node_sampler())

    # 为每层（除最后一层外）创建一个子节点数量采样器列表
    num_children_samplers = [
        uniform_int_sampler_f(1, min(h_config['max_num_children'], n_nodes_list[i + 1]),
                             seed=None if seed is None else seed + 100 + i)
        for i in range(len(n_nodes_list) - 1)
    ]

    # 采样各种参数
    noise_std = (h_config['min_noise_std'] if h_config['min_noise_std'] == h_config['max_noise_std']
                else uniform_sampler_f(h_config['min_noise_std'], h_config['max_noise_std'],
                                     seed=None if seed is None else seed + 200)())

    init_std = uniform_sampler_f(h_config['min_init_std'], h_config['max_init_std'],
                               seed=None if seed is None else seed + 300)()

    output_multiclass_ordered_p = uniform_sampler_f(h_config['min_output_multiclass_ordered_p'],
                                                   h_config['max_output_multiclass_ordered_p'],
                                                   seed=None if seed is None else seed + 400)()

    num_samples = uniform_int_sampler_f(h_config['min_num_samples'], h_config['max_num_samples'],
                                      seed=None if seed is None else seed + 500)()

    drop_node_ratio = uniform_sampler_f(h_config['min_drop_node_ratio'], h_config['max_drop_node_ratio'],
                                      seed=None if seed is None else seed + 600)()

    num_classes = uniform_int_sampler_f(2, h_config['max_num_classes'],
                                      seed=None if seed is None else seed + 700)()

    # 创建配置字典
    config = {
        'device': h_config['device'],
        'n_nodes_list': n_nodes_list,
        'noise_std': noise_std,
        'init_std': init_std,
        'num_children_samplers': num_children_samplers,
        'min_root': h_config['min_root'],
        'max_root': h_config['max_root'],
        'max_range': h_config['max_range'],
        'num_classes': num_classes,
        'output_multiclass_ordered_p': output_multiclass_ordered_p,
        'num_samples': num_samples,
        'task': h_config['task'],
        'sample_std': h_config['sample_std'],
        'categorical_feature_p': h_config['categorical_feature_p'],
        'drop_node_ratio': drop_node_ratio,
        'sample_cause_ranges': h_config['sample_cause_ranges'],
        'root_distribution': h_config.get('root_distribution', 'uniform'),
        'root_mean': h_config.get('root_mean', 0.0),
        'root_std': h_config.get('root_std', 1.0),
        'sample_root_std': h_config.get('sample_root_std', False),
        'train_test_split_ratio': h_config.get('train_test_split_ratio', 0.5),
        'use_monte_carlo_precompute': h_config.get('use_monte_carlo_precompute', False),
        'monte_carlo_samples': h_config.get('monte_carlo_samples', 10000),
        'monte_carlo_root_seed': h_config.get('monte_carlo_root_seed', 42),
        'monte_carlo_noise_seed': h_config.get('monte_carlo_noise_seed', 123),
        'monte_carlo_resample': h_config.get('monte_carlo_resample', True),
        'monte_carlo_resample_size': h_config.get('monte_carlo_resample_size', 1000),
    }

    return config


def generate_datasets(num_dataset, h_config,
                     perturbation_type=None, perturbation_node_type=None, perturbation_value_method='sample',
                     intervention_type=None, intervention_node_type=None, intervention_value_method='sample',
                     node_unobserved=False, unobserved_nodes=None, custom_dag_type=None, custom_edges=None,
                     custom_dag_size='small', remove_isolated_nodes=False, seed=None, seeds_config=None,
                     allow_skip=True, custom_functions=None, predefined_intervention_nodes=None,
                     predefined_perturbation_nodes=None, predefined_unobserved_nodes=None,
                     avg_in_degree=(1.2, 1.7)):
    """
    生成多个数据集

    参数:
        num_dataset: 要生成的数据集数量
        h_config: 高级配置字典
        perturbation_type: 扰动类型，如果不为None，则生成扰动数据
        perturbation_node_type: 扰动节点类型
        perturbation_value_method: 扰动值的生成方法
        intervention_type: 干预类型，如果不为None，则生成干预数据
        intervention_node_type: 干预节点类型
        intervention_value_method: 干预值的生成方法
        node_unobserved: 控制不可观测节点的选择方式
        unobserved_nodes: 直接指定不可被观测的节点列表或集合
        custom_dag_type: 自定义DAG类型
        custom_edges: 自定义边列表
        custom_dag_size: 随机图的规模
        remove_isolated_nodes: 是否移除孤立节点
        seed: 全局随机种子
        seeds_config: 种子配置字典
        allow_skip: 是否允许跳过失败的数据集生成
        custom_functions: 自定义函数配置字典
        predefined_intervention_nodes: 预定义的干预节点列表
        predefined_perturbation_nodes: 预定义的扰动节点列表
        predefined_unobserved_nodes: 预定义的不可观测节点列表
        avg_in_degree: 随机图的平均入度范围

    返回:
        包含数据集信息的列表
    """
    # 创建数据集生成器实例
    generator = DatasetGenerator(h_config, seed, seeds_config, allow_skip)

    # 调用生成方法，传递所有参数
    return generator.generate_datasets(
        num_dataset=num_dataset,
        perturbation_type=perturbation_type,
        perturbation_node_type=perturbation_node_type,
        perturbation_value_method=perturbation_value_method,
        intervention_type=intervention_type,
        intervention_node_type=intervention_node_type,
        intervention_value_method=intervention_value_method,
        node_unobserved=node_unobserved,
        unobserved_nodes=unobserved_nodes,
        custom_dag_type=custom_dag_type,
        custom_edges=custom_edges,
        custom_dag_size=custom_dag_size,
        remove_isolated_nodes=remove_isolated_nodes,
        custom_functions=custom_functions,
        predefined_intervention_nodes=predefined_intervention_nodes,
        predefined_perturbation_nodes=predefined_perturbation_nodes,
        predefined_unobserved_nodes=predefined_unobserved_nodes,
        avg_in_degree=avg_in_degree
    )


def generate_custom_dag(structure_type='common_cause', custom_edges=None,
                       size='small', seed=None, remove_isolated_nodes=False,
                       predefined_intervention_nodes=None, predefined_perturbation_nodes=None,
                       predefined_unobserved_nodes=None, avg_in_degree=(1.2, 1.7)):
    """
    生成预定义结构的DAG图或根据自定义边列表生成DAG图
    """
    return DAGFactory.create_dag(
        structure_type=structure_type,
        custom_edges=custom_edges,
        size=size,
        seed=seed,
        remove_isolated_nodes=remove_isolated_nodes,
        predefined_intervention_nodes=predefined_intervention_nodes,
        predefined_perturbation_nodes=predefined_perturbation_nodes,
        predefined_unobserved_nodes=predefined_unobserved_nodes,
        avg_in_degree=avg_in_degree
    )


def save_datasets_to_csv(datasets, output_dir='./data', use_timestamp=True):
    """将生成的数据集保存为CSV文件）"""
    return DataSaver.save_datasets_to_csv(datasets, output_dir, use_timestamp)


def get_dataset_seed(dataset_index, seed_type, seed=None, seeds_config=None):
    """根据种子系统配置获取指定数据集和种子类型的种子"""
    return SeedManager.get_dataset_seed(dataset_index, seed_type, seed, seeds_config)

