import networkx as nx
import random
import numpy as np
import scipy.stats as stats
import matplotlib

matplotlib.use('Agg')
import matplotlib.pyplot as plt

import torch
import copy
import torch.nn as nn
import math
import os
from contextlib import contextmanager

from function_generator import FunctionGenerator
from utils_scm import (
    causes_sampler_f,
    class_sampler_f,
    randomize_classes,
    draw_graph,
    get_parents,
    get_children,
    get_spouses,
    get_all_descendants,
    get_grandparents,
    get_all_antecedents,
    get_siblings,
    get_exclusive_node_relationships,
    classify_nodes_by_target_relationship_type,
    classify_edges_by_target_relationship,
    detect_node_type_config,
    get_node_type_for_node,
    get_edge_function_config,
    uniform_sampler_f
)

class SkipDatasetException(Exception):
    """
    自定义异常，用于表示跳过当前数据集生成的情况
    当DAG生成或目标节点选择失败时抛出此异常，数据生成器会捕获并跳过当前数据集
    """
    def __init__(self, message):
        super().__init__(message)

class SeedManager:
    """随机种子管理工具类，提供统一的种子设置和恢复功能"""
    
    @staticmethod
    @contextmanager
    def seed_context(seed, offset=0):
        """
        随机种子上下文管理器
        
        参数:
            seed: 基础种子，如果为None则不设置种子
            offset: 种子偏移量
        """
        if seed is None:
            yield
            return
            
        # 保存当前状态
        python_state = random.getstate()
        numpy_state = np.random.get_state()
        torch_state = torch.get_rng_state()
        cuda_state = None
        if torch.cuda.is_available():
            cuda_state = torch.cuda.get_rng_state()
        
        try:
            # 设置新种子
            actual_seed = seed + offset
            random.seed(actual_seed)
            np.random.seed(actual_seed)
            torch.manual_seed(actual_seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(actual_seed)
            
            yield actual_seed
            
        finally:
            # 恢复原始状态
            random.setstate(python_state)
            np.random.set_state(numpy_state)
            torch.set_rng_state(torch_state)
            if torch.cuda.is_available() and cuda_state is not None:
                torch.cuda.set_rng_state(cuda_state)

# 设置随机种子函数
def set_random_seed(seed=None):
    """设置Python、NumPy和PyTorch的随机种子，确保结果可复现

    参数:
        seed: 随机种子，如果为None则不设置
    返回:
        设置的随机种子
    """
    if seed is not None:
        random.seed(seed)
        np.random.seed(seed)
        torch.manual_seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)  # 如果使用多GPU
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
    return seed

# Generate tree-based DAG
# 定义一个函数，用于生成基于树的有向无环图 (DAG)
def generate_dag(n_nodes_list, num_children_samplers, seed=None):
    # 如果提供了种子，设置随机种子
    if seed is not None:
        # 保存当前随机状态
        np_state = np.random.get_state()
        # 设置种子
        np.random.seed(seed)

    # 创建一个空的有向图对象
    G = nx.DiGraph()
    # 获取层数
    num_layer = len(n_nodes_list)
    # 初始化一个空列表，用于存储每层的节点
    layer_nodes = []
    # 初始化节点索引
    node_idx = 0

    # 为每一层生成节点
    for i in range(num_layer):
        # 生成当前层的节点列表，节点编号从 node_idx 开始
        node_list = np.arange(node_idx, node_idx + n_nodes_list[i])
        # 将当前层的节点添加到图中
        G.add_nodes_from(node_list)
        # 将当前层的节点列表添加到 layer_nodes 中
        layer_nodes.append(node_list)
        # 更新节点索引，为下一层做准备
        node_idx += n_nodes_list[i]

    # 为每层（除最后一层外）的节点添加边
    for i in range(num_layer - 1):
        # 使用对应的子节点数量采样器获取当前层节点应有的子节点数量
        # 如果提供了种子，使用不同的子种子避免冲突
        layer_seed = None if seed is None else seed + i * 100
        num_children = num_children_samplers[i]()
        # 遍历当前层的每个节点
        for j, node in enumerate(layer_nodes[i]):
            # 为每个节点生成不同的种子
            node_seed = None if seed is None else seed + i * 100 + j
            # 从下一层节点中随机选择 num_children 个不重复的节点作为子节点
            with SeedManager.seed_context(seed, i * 100 + j):
                children = np.random.choice(layer_nodes[i + 1], size=num_children, replace=False)
            # 添加从当前节点到其子节点的边
            G.add_edges_from([(node, child) for child in children])

    # 识别图中的根节点（入度为 0 的节点）
    root_nodes = [node for node in G.nodes if G.in_degree(node) == 0]

    # 如果提供了种子，恢复原始随机状态
    if seed is not None:
        np.random.set_state(np_state)

    # 返回生成的 DAG、根节点列表和每层节点列表
    return G, root_nodes, layer_nodes


class NodeSelector:
    """节点选择工具类，统一处理各种节点选择逻辑"""
    
    @staticmethod
    def select_nodes_by_type(dag, target_node, node_type, seed=None, offset=0):
        """
        根据节点类型选择节点
        
        参数:
            dag: DAG图
            target_node: 目标节点
            node_type: 节点类型
            seed: 随机种子
            offset: 种子偏移量
            
        返回:
            选中的节点集合
        """
        # 使用互斥分类函数获取各种关系节点
        relationships = get_exclusive_node_relationships(dag, target_node)
        intervention_nodes = set()

        # 单个节点选择的类型
        single_node_types = {
            'single_parent': relationships['parents'],
            'single_child': relationships['children'],
            'single_spouse': relationships['spouses'],
            'single_grandparent': relationships['grandparents'],
            'single_sibling': relationships['siblings']
        }
        
        # 所有节点选择的类型
        all_node_types = {
            'all_parents': relationships['parents'],
            'all_children': relationships['children'],
            'all_spouses': relationships['spouses'],
            'all_grandparents': relationships['grandparents'],
            'all_siblings': relationships['siblings'],
            'all_family': (relationships['parents'] | relationships['children'] | relationships['spouses'])
        }
        
        # 非节点选择的类型
        non_node_types = {
            'all_non_parents': set(dag.nodes) - relationships['parents'] - {target_node},
            'all_non_children': set(dag.nodes) - relationships['children'] - {target_node},
            'all_non_grandparents': set(dag.nodes) - relationships['grandparents'] - {target_node},
            'all_non_spouses': set(dag.nodes) - relationships['spouses'] - {target_node},
            'all_non_siblings': set(dag.nodes) - relationships['siblings'] - {target_node},
            'all_non_family': set(dag.nodes) - (relationships['parents'] | relationships['children'] | relationships['spouses'] | {target_node})
        }

        # 处理单个节点选择
        if node_type in single_node_types:
            candidates = single_node_types[node_type]
            if candidates:
                with SeedManager.seed_context(seed, offset + 100):
                    chosen = np.random.choice(sorted(list(candidates)))
                intervention_nodes.add(chosen)
        
        # 处理所有节点选择
        elif node_type in all_node_types:
            intervention_nodes = all_node_types[node_type]
        
        # 处理非节点选择
        elif node_type in non_node_types:
            intervention_nodes = non_node_types[node_type]

        return intervention_nodes

    @staticmethod
    def select_unobserved_nodes(dag, target_node, node_unobserved, intervention_nodes, 
                               custom_unobserved_nodes=None, seed=None):
        """
        统一的不可观测节点选择逻辑
        
        参数:
            dag: DAG图
            target_node: 目标节点
            node_unobserved: 不可观测节点配置
            intervention_nodes: 干预/扰动节点
            custom_unobserved_nodes: 自定义不可观测节点
            seed: 随机种子
            
        返回:
            不可观测节点集合
        """
        # 如果提供了自定义的不可观测节点，直接使用
        if custom_unobserved_nodes is not None:
            if isinstance(custom_unobserved_nodes, (list, tuple)):
                return set(custom_unobserved_nodes)
            elif isinstance(custom_unobserved_nodes, set):
                return custom_unobserved_nodes
            else:
                return {custom_unobserved_nodes}  # 单个节点转换为集合

        # 如果node_unobserved为False，表示所有节点都可被观测
        if node_unobserved is False:
            return set()

        # 如果node_unobserved为True或'intervention_nodes'，表示干预/扰动节点不可被观测
        if node_unobserved is True or node_unobserved == 'intervention_nodes':
            return set(intervention_nodes)

        # 处理其他复杂的不可观测节点选择逻辑
        return NodeSelector._handle_complex_unobserved_selection(
            dag, target_node, node_unobserved, intervention_nodes, seed
        )
    
    @staticmethod
    def _handle_complex_unobserved_selection(dag, target_node, node_unobserved, intervention_nodes, seed):
        """处理复杂的不可观测节点选择逻辑"""

        # 处理干预/扰动节点的部分不可观测
        if isinstance(node_unobserved, str) and node_unobserved.startswith('intervention_'):
            # 如果指定了干预/扰动节点的部分不可观测
            if node_unobserved == 'intervention_nodes':
                # 所有干预/扰动节点不可观测
                return set(intervention_nodes)
            elif node_unobserved.startswith('intervention_n_'):
                # 选择n个干预/扰动节点不可观测
                try:
                    n = int(node_unobserved.split('_')[-1])
                    n = min(n, len(intervention_nodes))  # 确保n不超过可用节点数
                    if n <= 0 or not intervention_nodes:
                        return set()
                    # 使用SeedManager确保可重现性
                    with SeedManager.seed_context(seed, 5000):
                        return set(np.random.choice(sorted(list(intervention_nodes)), size=n, replace=False))
                except (ValueError, IndexError):
                    print(f"警告: 无效的intervention_n格式 '{node_unobserved}'，使用所有干预/扰动节点")
                    return set(intervention_nodes)
            elif node_unobserved.startswith('intervention_ratio_'):
                # 选择一定比例的干预/扰动节点不可观测
                try:
                    ratio = float(node_unobserved.split('_')[-1])
                    ratio = max(0, min(1, ratio))  # 确保ratio在0到1之间
                    if not intervention_nodes:
                        return set()
                    n = max(1, int(len(intervention_nodes) * ratio))  # 至少选择1个
                    n = min(n, len(intervention_nodes))  # 确保n不超过可用节点数
                    # 使用SeedManager确保可重现性
                    with SeedManager.seed_context(seed, 5100):
                        return set(np.random.choice(sorted(list(intervention_nodes)), size=n, replace=False))
                except (ValueError, IndexError):
                    print(f"警告: 无效的intervention_ratio格式 '{node_unobserved}'，使用所有干预/扰动节点")
                    return set(intervention_nodes)

        # 随机选择n个节点不可被观测
        if isinstance(node_unobserved, str) and node_unobserved.startswith('random_'):
            all_nodes = set(dag.nodes())
            # 排除目标节点，确保目标节点始终可被观测
            all_nodes.discard(target_node)

            if node_unobserved.startswith('random_n_'):
                try:
                    n = int(node_unobserved.split('_')[-1])
                    n = min(n, len(all_nodes))  # 确保n不超过可用节点数
                    # 使用SeedManager确保可重现性
                    with SeedManager.seed_context(seed, 4000):
                        return set(np.random.choice(list(all_nodes), size=n, replace=False))
                except (ValueError, IndexError):
                    print(f"警告: 无效的random_n格式 '{node_unobserved}'，使用默认值1")
                    with SeedManager.seed_context(seed, 4000):
                        return set(np.random.choice(list(all_nodes), size=1, replace=False))

            elif node_unobserved.startswith('random_ratio_'):
                try:
                    ratio = float(node_unobserved.split('_')[-1])
                    ratio = max(0, min(1, ratio))  # 确保ratio在0到1之间
                    n = int(len(all_nodes) * ratio)
                    # 使用SeedManager确保可重现性
                    with SeedManager.seed_context(seed, 4100):
                        return set(np.random.choice(list(all_nodes), size=n, replace=False))
                except (ValueError, IndexError):
                    print(f"警告: 无效的random_ratio格式 '{node_unobserved}'，使用默认值0.1")
                    n = int(len(all_nodes) * 0.1)
                    with SeedManager.seed_context(seed, 4100):
                        return set(np.random.choice(list(all_nodes), size=n, replace=False))

        # 处理基于图结构关系的不可观测节点选择
        relation_types = {
            'parents': lambda: get_parents(dag, target_node),
            'children': lambda: get_children(dag, target_node),
            'siblings': lambda: get_siblings(dag, target_node),
            'spouses': lambda: get_spouses(dag, target_node),
            'non_parents': lambda: set(dag.nodes()) - get_parents(dag, target_node) - {target_node},
            'non_children': lambda: set(dag.nodes()) - get_children(dag, target_node) - {target_node},
            'non_siblings': lambda: set(dag.nodes()) - get_siblings(dag, target_node) - {target_node},
            'non_spouses': lambda: set(dag.nodes()) - get_spouses(dag, target_node) - {target_node},
            'markov_blanket': lambda: get_parents(dag, target_node) | get_children(dag, target_node) | get_spouses(dag, target_node),
        }

        # 检查是否是基本关系类型
        for relation_type, get_nodes_func in relation_types.items():
            # 完全匹配关系类型（选择所有相关节点）
            if node_unobserved == relation_type:
                return get_nodes_func()

            # 检查是否是 'relation_type_n' 格式（选择n个相关节点）
            if isinstance(node_unobserved, str) and node_unobserved.startswith(f"{relation_type}_"):
                suffix = node_unobserved[len(relation_type)+1:]

                # 获取该关系类型的所有节点
                all_related_nodes = get_nodes_func()
                if not all_related_nodes:  # 如果没有相关节点，返回空集
                    return set()

                # 检查是否是数字格式（选择n个节点）
                if suffix.isdigit():
                    try:
                        n = int(suffix)
                        n = min(n, len(all_related_nodes))  # 确保n不超过可用节点数
                        if n <= 0:
                            return set()
                        # 使用SeedManager确保可重现性
                        with SeedManager.seed_context(seed, 6000):
                            return set(np.random.choice(sorted(list(all_related_nodes)), size=n, replace=False))
                    except (ValueError, IndexError):
                        print(f"警告: 无效的数字格式 '{suffix}'，使用所有节点")
                        return all_related_nodes
                # 检查是否是比例格式（选择一定比例的节点）
                elif suffix.startswith('ratio_'):
                    try:
                        ratio_str = suffix[6:]  # 去掉 'ratio_' 前缀
                        ratio = float(ratio_str)
                        ratio = max(0, min(1, ratio))  # 确保ratio在0到1之间
                        n = int(len(all_related_nodes) * ratio)
                        if n <= 0:
                            return set()
                        # 使用SeedManager确保可重现性
                        with SeedManager.seed_context(seed, 6100):
                            return set(np.random.choice(sorted(list(all_related_nodes)), size=n, replace=False))
                    except (ValueError, IndexError):
                        print(f"警告: 无效的比例格式 '{suffix}'，使用所有节点")
                        return all_related_nodes
                else:
                    print(f"警告: 无效的节点选择格式 '{node_unobserved}'，使用所有节点")
                    return all_related_nodes

        # 默认情况下返回空集合，表示所有节点都可被观测
        return set()


# Generate assignment functions
# 定义一个函数，用于为 DAG 中的非根节点生成赋值函数
def generate_assignment(G, noise_std, init_std, device, sample_std=False,
                        hidden_dim=None, noise_type='gaussian', seed=None, node_seed_indices=None,
                        custom_functions=None, target_node=None):
    # 定义一个内部类 AssingmentGenerator，继承自 nn.Module，用于生成赋值函数
    class AssingmentGenerator(nn.Module):
        def __init__(self, in_dim, out_dim, noise_std, init_std, device,
                     hidden_dim, noise_type, seed=None, custom_function_config=None):
            '''
            参数:
                in_dim: 输入维度 (父节点数量)
                out_dim: 输出维度 (通常为 1)
                noise_std: 噪声的标准差
                init_std: MLP 参数初始化的标准差
                device: 计算设备
                hidden_dim: MLP隐藏层维度，默认为in_dim
                noise_type: 噪声分布类型，可选'gaussian'、'laplace'、'uniform'、'student_t'
                custom_function_config: 自定义函数配置，必须提供
                    支持的噪声均值配置:
                    - noise_mean: 固定噪声均值 (默认: 0)
                    - noise_mean_mode: 'fixed' (固定值) 或 'signal_mean' (使用信号均值)
            '''
            super(AssingmentGenerator, self).__init__() # super().__init__() 是 PyTorch 神经网络模块的"入场券"，确保自定义类能够正确继承和使用 nn.Module 的所有功能。

            # 如果未指定隐藏层维度，使用输入维度作为默认值
            if hidden_dim is None:
                hidden_dim = in_dim

            # 保存种子
            self.seed = seed

            # 保存自定义函数配置
            self.custom_function_config = custom_function_config

            # 检查是否提供了自定义函数配置，如果没有则抛出错误
            if custom_function_config is None:
                raise ValueError("必须提供自定义函数配置 (custom_function_config)，不再支持传统的随机MLP模式")

            # 初始化噪声均值配置
            self.noise_mean = custom_function_config.get('noise_mean', 0.0)
            self.noise_mean_mode = custom_function_config.get('noise_mean_mode', 'fixed')
            self.signal_mean = None  # 信号均值，在forward时计算

            if self.noise_mean_mode not in ['fixed', 'signal_mean']:
                print(f"警告: 不支持的噪声均值模式 '{self.noise_mean_mode}'，使用默认的 'fixed' 模式")
                self.noise_mean_mode = 'fixed'

            # 设置输入和输出维度
            self.in_dim = in_dim
            self.out_dim = out_dim
            self.hidden_dim = hidden_dim
            self.noise_type = noise_type

            # 设置噪声标准差（必须在_setup_custom_function之前设置）
            self.noise_std = noise_std

            # 初始化噪声样本存储，用于统计计算
            self.last_noise_sample = None

            # 设置函数结构 - 只支持自定义函数模式
            self._setup_custom_function(device, init_std)

            # 保存init_std到实例属性
            self.init_std = init_std

        def _setup_custom_function(self, device, init_std):
            """设置自定义函数"""

            self.f_function, self.selected_f_name = FunctionGenerator.create_f_function(
                self.custom_function_config,
                self.in_dim,
                self.out_dim,
                device,
                init_std,
                seed = self.seed
            )

            if self.f_function is None:
                raise ValueError(f"无法创建自定义函数，配置: {self.custom_function_config}")


            # 处理通用的SNR和噪声配置
            target_snr = self.custom_function_config.get('target_snr', None)
            noise_std_override = self.custom_function_config.get('noise_std', None)

            # 保存原始噪声标准差（在任何修改之前）
            self.original_noise_std = self.noise_std

            # 如果指定了目标SNR，保存以便后续计算噪声
            if target_snr is not None:
                self.target_snr = target_snr
                print(f"节点自定义函数配置了目标SNR: {target_snr}")

            # 如果指定了噪声标准差且没有目标SNR，覆盖默认值
            if noise_std_override is not None and target_snr is None:
                self.noise_std = noise_std_override
                print(f"节点自定义函数使用固定噪声标准差: {noise_std_override}")
            elif target_snr is not None:
                print(f"节点自定义函数将根据目标SNR={target_snr}动态计算噪声标准差")

            # 设置g函数（后非线性变换）
            g_function_config = self.custom_function_config.get('g_function_config', None)
            if g_function_config is None:
                # 如果没有g_function_config，检查g_function_type
                g_function_type = self.custom_function_config.get('g_function_type', 'identity')
                g_function_config = g_function_type

            self.g_function, self.selected_g_name = FunctionGenerator.create_g_function(
                g_function_config, device, init_std, seed=self.seed
            )
            print(f"节点自定义函数使用g函数: {self.selected_g_name}")

        def generate_noise(self, batch_size, signal_data=None):
            """
            根据指定的噪声类型生成噪声

            参数:
                batch_size: 批次大小
                signal_data: 信号数据，用于计算信号均值（当noise_mean_mode='signal_mean'时）

            返回:
                noise: 生成的噪声张量
            """
            # 确定噪声均值
            current_noise_mean = self.noise_mean
            if self.noise_mean_mode == 'signal_mean' and signal_data is not None:
                # 使用信号数据的均值作为噪声均值
                current_noise_mean = torch.mean(signal_data).item()
                self.signal_mean = current_noise_mean  # 保存信号均值用于后续分析

            # 使用种子上下文管理器
            with SeedManager.seed_context(self.seed, 2):
                # 创建形状为 (batch_size, out_dim) 的噪声
                if self.noise_type == 'gaussian':
                    # 高斯噪声
                    noise = torch.normal(
                        mean=current_noise_mean * torch.ones((batch_size, self.out_dim), device=device),
                        std=self.noise_std
                    )
                elif self.noise_type == 'laplace':
                    # 拉普拉斯噪声
                    noise = torch.distributions.Laplace(
                        loc=current_noise_mean * torch.ones((batch_size, self.out_dim), device=device),
                        scale=self.noise_std / math.sqrt(2)
                    ).sample()
                elif self.noise_type == 'uniform':
                    # 均匀分布噪声，范围为 [mean-sqrt(3)*noise_std, mean+sqrt(3)*noise_std]
                    # 这样设置使得均匀分布的标准差等于noise_std，均值等于current_noise_mean
                    scale = self.noise_std * math.sqrt(3)
                    noise = torch.distributions.Uniform(
                        low=(current_noise_mean - scale) * torch.ones((batch_size, self.out_dim), device=device),
                        high=(current_noise_mean + scale) * torch.ones((batch_size, self.out_dim), device=device)
                    ).sample()
                elif self.noise_type == 'student_t':
                    # t分布噪声 (自由度为5)
                    # 注意：t分布的标准差是 sqrt(df/(df-2))，所以需要调整scale
                    df = 5  # 自由度
                    scale_adjustment = math.sqrt((df - 2) / df)
                    noise = torch.distributions.StudentT(
                        df=df
                    ).sample((batch_size, self.out_dim)).to(device) * (self.noise_std * scale_adjustment) + current_noise_mean
                else:
                    # 默认使用拉普拉斯噪声
                    noise = torch.distributions.Laplace(
                        loc=current_noise_mean * torch.ones((batch_size, self.out_dim), device=device),
                        scale=self.noise_std / math.sqrt(2)
                    ).sample()

            return noise

        def forward(self, x):
            # 在不计算梯度的情况下执行前向传播
            with torch.no_grad():
                # f函数的非线性输出
                f_output = self.f_function(x)

                # 生成噪声（如果需要使用信号均值，传递f_output）
                if self.noise_mean_mode == 'signal_mean':
                    noise = self.generate_noise(x.shape[0], signal_data=f_output)
                else:
                    noise = self.generate_noise(x.shape[0])

                # 保存噪声样本用于后续统计计算
                self.last_noise_sample = noise

                # 加性噪声
                noisy_output = f_output + noise

                # 应用g函数(后非线性变换)
                if callable(self.g_function) and not isinstance(self.g_function, nn.Module):
                    # 如果g_function是一个lambda函数
                    output = self.g_function(noisy_output)
                else:
                    output = self.g_function(noisy_output)

                # 返回浮点型的输出
                return output.float()

        def get_model_info(self):
            """返回模型的详细信息"""

            # 获取当前实际使用的噪声标准差
            current_noise_std = self.noise_std.item() if isinstance(self.noise_std, torch.Tensor) else self.noise_std

            # 获取原始配置的噪声标准差（如果有的话）
            original_noise_std = None
            if hasattr(self, 'original_noise_std'):
                original_noise_std = self.original_noise_std.item() if isinstance(self.original_noise_std, torch.Tensor) else self.original_noise_std

            info = {
                'f_function': self.selected_f_name,
                'g_function': self.selected_g_name,
                'noise_type': self.noise_type,
                'noise_std': current_noise_std,  # 当前实际使用的噪声标准差
                'original_noise_std': original_noise_std,  # 原始配置的噪声标准差
                'noise_mean': self.noise_mean,  # 噪声均值
                'noise_mean_mode': self.noise_mean_mode,  # 噪声均值模式
                'signal_mean': self.signal_mean,  # 信号均值（如果计算过的话）
                'hidden_dim': self.hidden_dim,
                'init_std': self.init_std if hasattr(self, 'init_std') else None,
                'custom_function_config': self.custom_function_config
            }
            # 详细f_function层信息
            f_layers = []
            if isinstance(self.f_function, torch.nn.Sequential):
                for layer in self.f_function:
                    if isinstance(layer, torch.nn.Linear):
                        f_layers.append({
                            'type': 'Linear',
                            'weight': layer.weight.detach().cpu().numpy().tolist(),
                            'bias': layer.bias.detach().cpu().numpy().tolist()
                        })
                    elif isinstance(layer, torch.nn.Module):
                        f_layers.append({
                            'type': type(layer).__name__
                        })
                    else:
                        f_layers.append({'type': str(type(layer))})
            elif isinstance(self.f_function, torch.nn.Linear):
                f_layers.append({
                    'type': 'Linear',
                    'weight': self.f_function.weight.detach().cpu().numpy().tolist(),
                    'bias': self.f_function.bias.detach().cpu().numpy().tolist()
                })
            info['f_layers'] = f_layers
            return info

    # 检查配置类型并进行相应的分类
    edge_classification = None
    node_classification = None
    config_type = None

    if custom_functions is not None and target_node is not None:
        # 检查是否是节点类型级配置
        is_node_type_config = detect_node_type_config(custom_functions, target_node)

        if is_node_type_config:
            # 使用节点类型级配置
            config_type = 'node_type'
            node_classification = classify_nodes_by_target_relationship_type(G, target_node)
            print(f"使用节点类型级函数配置，目标节点: {target_node}")
            print(f"节点分类结果: {node_classification}")
        else:
            # 检查是否是边级配置格式
            edge_type_keys = ['Yparent_Y', 'Y_Ychild', 'Yspouse_Ychild', 'Yparent_Ysibling', 'Ygrandparent_Yparent', 'Other']
            is_edge_based_config = any(key in custom_functions for key in edge_type_keys)

            if is_edge_based_config:
                # 使用边级配置
                config_type = 'edge'
                edge_classification = classify_edges_by_target_relationship(G, target_node)
                print(f"使用边级函数配置，目标节点: {target_node}")
                print(f"边分类结果: {edge_classification}")
            else:
                # 使用传统节点级配置
                config_type = 'node'
                print(f"使用传统节点级函数配置")

    # 初始化一个空字典，用于存储每个非根节点的赋值信息
    assignment_list = {}
    # 获取图中所有非根节点（入度不为 0 的节点）
    non_root_nodes = [node for node in G.nodes if G.in_degree(node) != 0]

    # 遍历每个非根节点
    for i, node in enumerate(non_root_nodes):
        # 获取当前节点的父节点列表
        parents = list(G.predecessors(node))

        # （1）节点噪声标准差计算：使用传统方法
        # 如果提供了种子并需要采样标准差
        if seed is not None and sample_std:
            # 使用种子上下文管理器
            with SeedManager.seed_context(seed, i * 100):
                # 采样噪声标准差
                noise_std_sample = torch.abs(torch.normal(torch.zeros(1), float(noise_std)))
        else:
            # 如果 sample_std 为 True，则从以 noise_std 为标准差的正态分布中采样一个值作为噪声标准差，并取绝对值
            # 否则，直接使用传入的 noise_std
            noise_std_sample = torch.abs(torch.normal(torch.zeros(1), float(noise_std))) if sample_std else noise_std

        # （2）MLP初始化参数标准差的处理（不受SNR方法影响）
        if seed is not None and sample_std:
            # 使用种子上下文管理器
            with SeedManager.seed_context(seed, i * 100):
                # 采样初始化标准差
                init_std_sample = torch.abs(torch.normal(torch.zeros(1), float(init_std))).item()
        else:
            # 如果 sample_std 为 True，则从以 init_std 为标准差的正态分布中采样一个值作为 MLP 初始化标准差，并取绝对值
            # 否则，直接使用传入的 init_std
            init_std_sample = torch.abs(torch.normal(torch.zeros(1), float(init_std))).item() if sample_std else init_std

        # 为每个节点生成不同的种子
        node_seed = None if seed is None else seed + i * 100

        # 获取当前节点的自定义函数配置（如果有）
        node_custom_function = None
        if custom_functions is not None:
            if node_classification is not None:
                # 节点类型级配置：根据节点类型获取配置
                node_type = get_node_type_for_node(node, node_classification)
                if node_type in custom_functions:
                    node_custom_function = custom_functions[node_type]
                    print(f"节点 {node} 使用节点类型 '{node_type}' 的配置: {node_custom_function.get('type', 'unknown')}")
                else:
                    print(f"节点 {node} 的类型 '{node_type}' 没有找到对应的配置")
            elif edge_classification is not None:
                # 边级配置：需要为该节点的每个输入边找到对应的配置
                # 当前是占位简化实现
                # 由于一个节点可能有多个输入边，我们需要选择一个配置
                # 这里采用优先级策略：优先使用第一个找到的配置
                edge_found = False
                for parent in parents:
                    edge = (parent, node)
                    edge_config = get_edge_function_config(edge, edge_classification, custom_functions)
                    if edge_config is not None:
                        node_custom_function = edge_config
                        print(f"节点 {node} 使用边 {edge} 的配置: {edge_config.get('type', 'unknown')}")
                        edge_found = True
                        break

                # 如果没有找到特定边的配置，尝试使用Other类型的配置（优先新命名）
                if not edge_found:
                    if 'Other_type' in custom_functions:
                        node_custom_function = custom_functions['Other_type']
                        print(f"节点 {node} 使用Other_type类型配置")
                        edge_found = True
                    elif 'Other' in custom_functions:
                        node_custom_function = custom_functions['Other']
                        print(f"节点 {node} 使用Other类型配置")
                        edge_found = True

                if not edge_found:
                    print(f"节点 {node} 没有找到匹配的边级配置")
            else:
                # 节点级配置
                if node in custom_functions:
                    node_custom_function = custom_functions[node]
                    print(f"节点 {node} 使用传统节点级配置: {node_custom_function.get('type', 'unknown')}")

        # 将节点的赋值信息存入字典，包括父节点列表和生成的 AssingmentGenerator 实例
        assignment_list[node] = {
            'parents': parents,
            'assignment': AssingmentGenerator(
                len(parents), 1, noise_std_sample, init_std_sample, device,
                hidden_dim=hidden_dim, noise_type=noise_type, seed=node_seed,
                custom_function_config=node_custom_function
            )
        }

    # 返回包含所有非根节点赋值信息的字典
    return assignment_list, None


# Transfer continuous data into discrete data
# 定义一个多类排序任务的 PyTorch 模块
class MulticlassRank(nn.Module):
    '''多类排序任务'''

    def __init__(self, num_classes, ordered_p=0.5, seed=None):
        super().__init__()
        # 使用 class_sampler_f 从 2 到 num_classes 之间采样实际使用的类别数量
        self.num_classes = class_sampler_f(2, num_classes, seed)()
        # 设置有序概率，用于决定是否随机化类别顺序
        self.ordered_p = ordered_p
        # 保存种子
        self.seed = seed

    def forward(self, x, class_boundaries=None):
        # x 的形状是 (T, B, H)，其中 T 通常是样本数量，B 是特征数量 (这里常为1)，H 是... (这里可能指单个值)
        # 注意：这个实现在批处理中为每个类边界采样相同的序列索引

        # 如果未提供类别边界
        if class_boundaries is None:
            # 使用种子上下文管理器
            with SeedManager.seed_context(self.seed, 3):
                # 从 x 的第一个维度（样本）中随机选择 num_classes - 1 个索引作为类别边界的原始值
                class_boundaries = torch.randint(0, x.shape[0], (self.num_classes - 1,))
                # 从 x 中获取这些边界值，并调整形状以进行广播比较
                class_boundaries = x[class_boundaries].unsqueeze(1)  # 形状变为 (num_classes-1, 1, H) or (num_classes-1, B, H)

        # 将输入 x 与类别边界进行比较，计算每个样本属于哪个类别
        # (x > class_boundaries) 的结果是布尔张量，sum(axis=0) 沿着类别边界维度求和
        # 得到每个样本的类别标签 (0 到 num_classes-1)
        d = (x > class_boundaries).sum(axis=0)  # 形状变为 (B, H)

        # 以下被注释掉的代码块用于随机化或反转类别顺序
        # randomized_classes = torch.rand((d.shape[1], )) > self.ordered_p
        # d[:, randomized_classes] = randomize_classes(d[:, randomized_classes], self.num_classes)
        # reverse_classes = torch.rand((d.shape[1],)) > 0.5
        # d[:, reverse_classes] = self.num_classes - 1 - d[:, reverse_classes]
        # 返回离散化后的类别标签和使用的类别边界
        return d, class_boundaries


# 定义一个多类值任务的 PyTorch 模块
class MulticlassValue(nn.Module):
    '''多类任务，列表随机化与反转'''

    def __init__(self, num_classes, ordered_p=0.5, seed=None):
        super().__init__()
        # 使用 class_sampler_f 从 2 到 num_classes 之间采样实际使用的类别数量
        self.num_classes = class_sampler_f(2, num_classes, seed)()

        # 使用种子上下文管理器初始化类别边界
        with SeedManager.seed_context(seed, 4):
            # 初始化 num_classes - 1 个类别边界值，作为模型的参数（但不需要梯度）
            self.classes = nn.Parameter(torch.randn(self.num_classes - 1), requires_grad=False)

        # 设置有序概率
        self.ordered_p = ordered_p
        # 保存种子
        self.seed = seed

    def forward(self, x):
        # x 的形状是 (T, B, H)
        # 将输入 x 与类别边界 self.classes 进行比较，计算类别标签
        # self.classes.unsqueeze(-1).unsqueeze(-1) 将边界调整形状以进行广播
        d = (x > (self.classes.unsqueeze(-1).unsqueeze(-1))).sum(axis=0)  # 形状变为 (B, H)

        # 使用种子上下文管理器进行随机化操作
        with SeedManager.seed_context(self.seed, 5):
            # 随机决定哪些批次/特征的类别顺序需要随机化
            randomized_classes = torch.rand((d.shape[1],)) > self.ordered_p
            # 对选定的批次/特征进行类别随机化
            d[:, randomized_classes] = randomize_classes(d[:, randomized_classes], self.num_classes, self.seed + 6)
            # 随机决定哪些批次/特征的类别顺序需要反转
            reverse_classes = torch.rand((d.shape[1],)) > 0.5
            # 对选定的批次/特征进行类别反转
            d[:, reverse_classes] = self.num_classes - 1 - d[:, reverse_classes]

        # 返回处理后的类别标签
        return d


class DataProcessor:
    """数据处理工具类，统一处理数据收集、分类任务等逻辑"""

    @staticmethod
    def collect_and_process_data(original_data, processed_data, dag, current_target_node,
                                current_selected_features, nodes_to_hide):
        """
        收集和处理原始数据与处理后数据

        参数:
            original_data: 原始数据字典
            processed_data: 处理后数据字典（扰动或干预后）
            dag: DAG图
            current_target_node: 当前目标节点
            current_selected_features: 当前选中的特征
            nodes_to_hide: 不可观测的节点

        返回:
            (x_original_full, y_original_full, x_processed_full, y_processed_full,
             selected_target_idx, selected_feature_indices)
        """
        # 创建节点到索引的映射
        node_to_idx = {node: i for i, node in enumerate(dag.nodes())}
        selected_target_idx = node_to_idx[current_target_node]

        # 从特征列表中移除不可观测的节点
        observed_features = current_selected_features.copy()
        if nodes_to_hide:
            observed_features = [feat for feat in observed_features if feat not in nodes_to_hide]

        selected_feature_indices = [node_to_idx[feat_node] for feat_node in observed_features if
                                    feat_node in node_to_idx]

        # 收集原始数据
        values_original = torch.cat([original_data[node] for node in dag.nodes()], dim=1)
        y_original_full = values_original[:, [selected_target_idx]]
        x_original_full = values_original[:, selected_feature_indices]

        # 收集处理后数据
        values_processed = torch.cat([processed_data[node] for node in dag.nodes()], dim=1)
        y_processed_full = values_processed[:, [selected_target_idx]]
        x_processed_full = values_processed[:, selected_feature_indices]

        return (x_original_full, y_original_full, x_processed_full, y_processed_full,
                selected_target_idx, selected_feature_indices)

    @staticmethod
    def handle_classification_task(y_original_full, y_processed_full, task, class_assigner):
        """
        处理分类任务的类别边界

        参数:
            y_original_full: 原始目标数据
            y_processed_full: 处理后目标数据
            task: 任务类型
            class_assigner: 分类器

        返回:
            (y_original_processed, y_processed_processed, class_boundaries)
        """
        class_boundaries_for_all = None

        if task == 'classification':
            y_original_full, class_boundaries_for_all = class_assigner(y_original_full)
            y_original_full = y_original_full.int().squeeze()

            y_processed_full, _ = class_assigner(y_processed_full, class_boundaries_for_all)
            y_processed_full = y_processed_full.int().squeeze()
        elif task == 'regression':
            y_original_full = y_original_full.float().squeeze()
            y_processed_full = y_processed_full.float().squeeze()

        return y_original_full, y_processed_full, class_boundaries_for_all

    @staticmethod
    def generate_final_output(operation_type, x_original_full, y_original_full,
                             x_processed_full, y_processed_full, class_boundaries_for_all,
                             current_target_node, current_selected_features,
                             num_samples, train_test_split_ratio, forward_func):
        """
        生成最终输出数据

        参数:
            operation_type: 操作类型 ('counterfactual' 或 'statistical')
            x_original_full, y_original_full: 原始数据
            x_processed_full, y_processed_full: 处理后数据
            class_boundaries_for_all: 分类边界
            current_target_node: 当前目标节点
            current_selected_features: 当前选中特征
            num_samples: 样本数量
            train_test_split_ratio: 训练测试分割比例
            forward_func: forward函数引用

        返回:
            (output_original, output_processed)
        """
        if operation_type == 'counterfactual':
            # 反事实：前半部分使用原始数据，后半部分使用处理后数据
            eval_position = int(num_samples * train_test_split_ratio)
            output_original = (x_original_full, y_original_full)
            output_processed = (
                torch.cat([x_original_full[:eval_position], x_processed_full[eval_position:]], dim=0),
                torch.cat([y_original_full[:eval_position], y_processed_full[eval_position:]], dim=0)
            )
        elif operation_type == 'statistical':
            # 统计：使用完全不同的数据集作为原始部分，但使用相同的目标/特征
            x_stat_original, y_stat_original, _ = forward_func(
                exist_boudries=class_boundaries_for_all,
                target_node=current_target_node,
                selected_features=current_selected_features
            )
            output_original = (x_stat_original, y_stat_original)
            output_processed = (x_processed_full, y_processed_full)
        else:
            raise ValueError(f"不支持的操作类型: {operation_type}")

        return output_original, output_processed


# 定义结构化因果模型 (SCM) 的 PyTorch 模块
class SCM(torch.nn.Module):
    def __init__(self, config, custom_dag=None, seed=None, allow_skip=True, custom_functions=None,
                 intervention_node_type=None, perturbation_node_type=None):
        """
        初始化SCM模型

        参数:
            config: 配置字典
            custom_dag: 自定义DAG
            seed: 随机种子，用于SCM模型内部的所有随机操作
            allow_skip: 是否允许跳过失败的生成
            custom_functions: 自定义函数配置字典，格式为 {node: function_config}
            intervention_node_type: 干预节点类型，用于目标节点选择
            perturbation_node_type: 扰动节点类型，用于目标节点选择（优先级高于intervention_node_type）
        """
        super(SCM, self).__init__()
        # 保存配置字典
        self.config = config
        # 将配置字典中的所有键值对设置为类的属性
        for key in config:
            setattr(self, key, config[key])

        # 保存随机种子
        self.seed = seed
        # 保存跳过相关配置
        self.allow_skip = allow_skip
        # 保存自定义函数配置
        self.custom_functions = custom_functions

        # 保存干预和扰动节点类型，用于目标节点选择
        self.intervention_node_type = intervention_node_type
        self.perturbation_node_type = perturbation_node_type

        # 确定用于目标节点选择的节点类型（扰动类型优先级更高）
        self.target_selection_node_type = perturbation_node_type if perturbation_node_type is not None else intervention_node_type

        # 根节点分布配置
        self.root_distribution = getattr(self, 'root_distribution', 'uniform')  # 'uniform' 或 'gaussian'
        self.root_mean = getattr(self, 'root_mean', 0.0)  # 高斯分布均值
        self.root_std = getattr(self, 'root_std', 1.0)  # 高斯分布标准差
        self.sample_root_std = getattr(self, 'sample_root_std', False)  # 是否采样标准差

        # 训练/测试数据分割比例配置
        self.train_test_split_ratio = getattr(self, 'train_test_split_ratio', 0.5)

        # 蒙特卡洛计算参数配置
        self.use_monte_carlo_precompute = getattr(self, 'use_monte_carlo_precompute', False)  # 是否进行蒙特卡洛计算
        self.monte_carlo_samples = getattr(self, 'monte_carlo_samples', 10000)  # 蒙特卡洛样本数
        self.monte_carlo_root_seed = getattr(self, 'monte_carlo_root_seed', 42)  # 采样根节点的蒙特卡洛种子
        self.monte_carlo_noise_seed = getattr(self, 'monte_carlo_noise_seed', 123)  # 采样噪声的蒙特卡洛种子
        self.monte_carlo_resample = getattr(self, 'monte_carlo_resample', True)  # 是否对父节点的蒙特卡洛样本重采样
        self.monte_carlo_resample_size = getattr(self, 'monte_carlo_resample_size', 1000)  # 重采样数量

        if seed is not None:
            set_random_seed(seed)

 # 如果提供了自定义DAG，则使用它
        if custom_dag is not None:
            if len(custom_dag) == 6:
                # 新格式：包含dag_info
                self.dag, self.root_nodes, self.layer_nodes, self.selected_target, self.selected_features, self.dag_info = custom_dag
            else:
                # 旧格式：不包含dag_info
                self.dag, self.root_nodes, self.layer_nodes, self.selected_target, self.selected_features = custom_dag
                self.dag_info = None
            # 标记是否使用预定义结构（非随机图）
            self.is_predefined_structure = self.selected_target is not None

            # 提取预定义配置（如果有）
            if self.dag_info is not None:
                self.predefined_intervention_nodes = self.dag_info.get('predefined_intervention_nodes')
                self.predefined_perturbation_nodes = self.dag_info.get('predefined_perturbation_nodes')
                self.predefined_unobserved_nodes = self.dag_info.get('predefined_unobserved_nodes')
            else:
                self.predefined_intervention_nodes = None
                self.predefined_perturbation_nodes = None
                self.predefined_unobserved_nodes = None
        else:
            # 否则根据配置生成 DAG、根节点和分层节点
            self.dag, self.root_nodes, self.layer_nodes = generate_dag(self.n_nodes_list, self.num_children_samplers, seed=seed)
            self.dag_info = None
            self.predefined_intervention_nodes = None
            self.predefined_perturbation_nodes = None
            self.predefined_unobserved_nodes = None

        # 创建节点到索引的映射，用于确定性随机种子
        self.node_to_idx = {node: idx for idx, node in enumerate(self.dag.nodes())}

        # 使用node_to_idx作为节点的种子索引映射
        self.node_seed_indices = self.node_to_idx

        # 如果配置要求采样原因节点的范围
        if self.sample_cause_ranges:
            # 为根节点采样初始化范围的下界 (lb) 和上界 (ub)
            self.lb, self.ub = causes_sampler_f(len(self.root_nodes), self.min_root, self.max_root, self.max_range, seed=self.seed)
            # 将下界和上界转换为张量，并调整形状以匹配样本数量和根节点数量
            self.lb = torch.tensor(self.lb, device=self.device).unsqueeze(0).expand(self.num_samples, -1)
            self.ub = torch.tensor(self.ub, device=self.device).unsqueeze(0).expand(self.num_samples, -1)

        # 初始化根节点分布参数
        self._init_root_distribution_params()

        # 如果没有提供自定义DAG，或者提供了随机图类型的自定义DAG（target_node为None），则需要选择目标节点和特征
        if custom_dag is None or self.selected_target is None:
            # 标记为非预定义结构（随机图）
            self.is_predefined_structure = False
            # 选择满足条件的目标节点，传递节点类型参数
            self.select_valid_target_node(node_type=self.target_selection_node_type)
            # 选择特征
            self.selected_features = self.select_features()
        else:
            # 如果是预定义结构，标记为True
            self.is_predefined_structure = True

        # 调试信息：检查目标节点和自定义函数配置
        print(f"调试信息 - 目标节点: {self.selected_target}")
        print(f"调试信息 - 自定义函数配置: {self.custom_functions}")

        assignment_result = generate_assignment(self.dag, self.noise_std, self.init_std, self.device, self.sample_std,
                                               seed=self.seed,
                                               node_seed_indices=self.node_seed_indices,  # 传递节点种子索引映射
                                               custom_functions=self.custom_functions,
                                               target_node=self.selected_target)

        # 处理返回结果
        if isinstance(assignment_result, tuple):
            self.assignment, self.snr_info = assignment_result
        else:
            self.assignment = assignment_result
            self.snr_info = None

        # 如果任务是分类任务
        if self.task == 'classification':
            # 初始化多类排序分配器
            self.class_assigner = MulticlassRank(self.num_classes,
                                                 ordered_p=self.output_multiclass_ordered_p)

    def _init_root_distribution_params(self):
        """初始化根节点分布参数"""
        if self.root_distribution == 'gaussian':
            # 如果需要采样标准差
            if self.sample_root_std:
                with SeedManager.seed_context(self.seed, 5000):
                    # 从正态分布中采样标准差并取绝对值
                    self.actual_root_std = abs(np.random.normal(0, self.root_std))
            else:
                self.actual_root_std = self.root_std
        else:
            # 对于均匀分布，不需要额外的标准差参数
            self.actual_root_std = None

    def _sample_root_data(self, num_samples, num_root_nodes, device):
        """
        根据配置的分布类型采样根节点数据

        Args:
            num_samples: 样本数量
            num_root_nodes: 根节点数量
            device: 设备

        Returns:
            torch.Tensor: 根节点数据
        """
        if self.sample_cause_ranges:
            # 如果配置要求采样原因节点的范围，则在 [lb, ub) 范围内采样
            if self.root_distribution == 'gaussian':
                # 高斯分布采样，然后缩放到指定范围
                root_data = torch.randn((num_samples, num_root_nodes), device=device) * self.actual_root_std + self.root_mean
                # 将数据缩放到 [lb, ub) 范围
                root_data = (root_data - root_data.min()) / (root_data.max() - root_data.min())
                root_data = (self.ub - self.lb) * root_data + self.lb
            else:
                # 均匀分布采样
                root_data = (self.ub - self.lb) * torch.rand((num_samples, num_root_nodes), device=device) + self.lb
        else:
            # 否则，根据分布类型采样
            if self.root_distribution == 'gaussian':
                # 高斯分布采样
                root_data = torch.normal(mean=self.root_mean, std=self.actual_root_std, size=(num_samples, num_root_nodes), device=device)
                # root_data = torch.randn((num_samples, num_root_nodes), device=device) * self.actual_root_std + self.root_mean
            else:
                # 均匀分布采样，在 [min_root, max_root) 范围内
                root_data = (self.max_root - self.min_root) * torch.rand((num_samples, num_root_nodes), device=device) + self.min_root

        return root_data.float()

    def get_root_distribution_info(self):
        """
        获取根节点分布信息

        Returns:
            dict: 包含分布类型、参数等信息的字典
        """
        info = {
            'distribution_type': self.root_distribution,
            'mean': self.root_mean if self.root_distribution == 'gaussian' else None,
            'std': self.actual_root_std if self.root_distribution == 'gaussian' else None,
            'sample_std': self.sample_root_std if self.root_distribution == 'gaussian' else None,
            'configured_std': self.root_std if self.root_distribution == 'gaussian' else None,
            'min_root': self.min_root if self.root_distribution == 'uniform' else None,
            'max_root': self.max_root if self.root_distribution == 'uniform' else None,
            'sample_cause_ranges': self.sample_cause_ranges
        }
        return info

    def get_node_statistics(self):
        """
        计算每个节点的统计信息（分位数、均值、标准差）

        Returns:
            dict: 每个节点的统计信息
        """
        if not hasattr(self, 'data') or not self.data:
            return {}

        node_stats = {}
        for node, data in self.data.items():
            if isinstance(data, torch.Tensor):
                data_np = data.cpu().numpy().flatten()
                node_stats[node] = {
                    'min': float(np.min(data_np)),
                    'q25': float(np.percentile(data_np, 25)),
                    'median': float(np.percentile(data_np, 50)),
                    'q75': float(np.percentile(data_np, 75)),
                    'max': float(np.max(data_np)),
                    'mean': float(np.mean(data_np)),
                    'std': float(np.std(data_np))
                }

        return node_stats

    def select_features(self, target_node=None):
        """
        选择特征节点，在每一层随机选择一部分作为特征不变，
        在最后一层添加更多特征（如果节点数大于1）

        参数:
            target_node: 目标节点，确保其不在选中的特征中

        返回:
            selected_features: 选中的特征节点列表
        """
        # 初始化选中的特征列表
        selected_features = []

        # 如果没有提供目标节点，使用类的selected_target属性
        if target_node is None:
            target_node = self.selected_target

        # 使用种子上下文管理器确保特征选择的可重现性
        with SeedManager.seed_context(self.seed, 8000):
            # 如果drop_node_ratio为0，选择所有非目标节点作为特征
            if self.drop_node_ratio == 0:
                # 选择所有节点，除了目标节点
                all_nodes = list(self.dag.nodes())
                selected_features = [node for node in all_nodes if node != target_node]
            else:
                # 遍历除最后一层外的所有层
                for layer in range(len(self.layer_nodes) - 1):
                    # 从当前层节点中随机选择一部分作为特征，选择比例为 (1 - drop_node_ratio)
                    num_to_select = round(len(self.layer_nodes[layer]) * (1 - self.drop_node_ratio))
                    if num_to_select > 0 and len(self.layer_nodes[layer]) > 0:
                        # 对节点进行排序以确保确定性的选择顺序
                        sorted_layer_nodes = sorted(self.layer_nodes[layer])
                        selected_features += list(np.random.choice(sorted_layer_nodes,
                                                                  size=min(num_to_select, len(sorted_layer_nodes)),
                                                                  replace=False))

                # 如果最后一层节点数大于1，则从最后一层中添加更多特征
                if len(self.layer_nodes[-1]) > 1:
                    # 计算要选择的特征数量，确保至少选择 (1-drop_node_ratio) 比例的节点
                    num_features_to_select = round(len(self.layer_nodes[-1]) * (1 - self.drop_node_ratio))
                    # 确保选择数量不为负
                    num_features_to_select = max(0, num_features_to_select)
                    # 从最后一层中随机选择特征
                    potential_features = sorted(list(self.layer_nodes[-1]))  # 排序以确保确定性
                    if len(potential_features) > 0 and num_features_to_select > 0:
                        selected_features += list(
                            np.random.choice(potential_features, size=min(num_features_to_select, len(potential_features)),
                                             replace=False))

                # 确保目标节点不在选中的特征中
                if target_node in selected_features:
                    selected_features.remove(target_node)

        return selected_features

    # 选择有效的目标节点
    def select_valid_target_node(self, node_type=None, min_parents=1, min_children=0, min_neighbors=0):
        """
        选择有效的目标节点，确保目标节点局部结构足够稠密

        参数:
            node_type: 干预/扰动节点类型，用于确定目标节点的条件
            min_parents: 目标节点的最小入度
            min_children: 目标节点的最小出度
            min_neighbors: 目标节点的最小邻居总数(父节点+子节点+配偶)
        """
        # 目标节点不能是根节点（入度必须大于0）
        valid_targets = []

        # 记录不满足条件的原因
        rejection_reasons = {}

        # 特定类型或类型为None时，在最后一层选择目标节点
        special_types = ['single_parent', 'all_parents', 'all_non_parents',
                         'single_sibling', 'all_siblings', 'all_non_siblings',
                         'single_grandparent', 'all_grandparents', 'all_non_grandparents', ]

        # 确定初始搜索层
        if node_type is None or node_type in special_types:
            # 在最后一层开始搜索
            initial_layer = len(self.layer_nodes) - 1
        else:
            # 在倒数第二层开始搜索
            if len(self.layer_nodes) >= 2:  # 确保至少有两层
                initial_layer = len(self.layer_nodes) - 2
            else:
                # 如果只有一层，则只能在最后一层搜索
                initial_layer = len(self.layer_nodes) - 1

        # 生成可搜索的层索引列表，从初始层开始倒序搜索，跳过第一层（根节点层）
        searchable_layers = []

        # 首先检查初始层是否有效
        if initial_layer > 0 and initial_layer < len(self.layer_nodes) and len(self.layer_nodes[initial_layer]) > 0:
            searchable_layers.append(initial_layer)

        # 生成其余可搜索的层（从初始层的上一层开始，到第二层）
        for i in range(initial_layer - 1, 0, -1):
            if len(self.layer_nodes[i]) > 0:
                searchable_layers.append(i)

        # 如果没有可搜索的层，直接跳过当前数据集
        if not searchable_layers:
            print("警告: 没有可搜索的非空层（除了根节点层），跳过当前数据集生成")
            if self.allow_skip:
                raise SkipDatasetException("没有可搜索的非空层（除了根节点层）")
            else:
                raise ValueError("没有可搜索的非空层（除了根节点层）")

        # 逐层搜索，一旦在某一层找到有效节点就停止搜索
        for layer_idx in searchable_layers:
            # 当前层的有效目标节点
            layer_valid_targets = []

            # 对节点进行排序以确保确定性的遍历顺序
            for node in sorted(self.layer_nodes[layer_idx]):
                # 获取节点的父节点和子节点
                parents = get_parents(self.dag, node)
                children = get_children(self.dag, node)

                # 记录该节点的拒绝原因
                node_rejection_reasons = []

                # 基本条件：必须有足够的父节点和子节点（控制局部稠密度）
                if len(parents) < min_parents:
                    node_rejection_reasons.append(f"父节点数量不足: {len(parents)} < {min_parents}")
                    rejection_reasons[node] = node_rejection_reasons.copy()
                    continue  # 父节点数量不足

                if len(children) < min_children:
                    node_rejection_reasons.append(f"子节点数量不足: {len(children)} < {min_children}")
                    rejection_reasons[node] = node_rejection_reasons.copy()
                    continue  # 子节点数量不足

                # 计算总邻居数（父节点+子节点+配偶）
                spouses = get_spouses(self.dag, node)
                total_neighbors = len(parents) + len(children) + len(spouses)

                if total_neighbors < min_neighbors:
                    node_rejection_reasons.append(f"总邻居数不足: {total_neighbors} < {min_neighbors}")
                    rejection_reasons[node] = node_rejection_reasons.copy()
                    continue  # 总邻居数不足

                # 根据不同的节点类型设置额外条件
                if node_type in ['single_child', 'all_children', 'all_non_children']:
                    # 需要有更多子节点，确保子节点选择的多样性
                    if len(children) < 1:
                        node_rejection_reasons.append(f"子节点类型需求不满足: {len(children)} < 1")
                        rejection_reasons[node] = node_rejection_reasons.copy()
                        continue
                elif node_type in ['all_non_parents']:
                    # 需要有足够的父亲节点
                    min_required_parents = (len(self.layer_nodes) - 1) // 2
                    if len(parents) < min_required_parents:
                        node_rejection_reasons.append(
                            f"非父节点类型需求不满足: {len(parents)} < {min_required_parents}")
                        rejection_reasons[node] = node_rejection_reasons.copy()
                        continue
                elif node_type in ['single_spouse', 'all_spouses', 'all_family', 'all_non_family', 'all_non_spouses']:
                    # 需要有足够的配偶节点
                    if len(spouses) < 1:
                        node_rejection_reasons.append(f"配偶节点类型需求不满足: {len(spouses)} < 1")
                        rejection_reasons[node] = node_rejection_reasons.copy()
                        continue
                elif node_type in ['single_grandparent', 'all_grandparents', 'all_non_grandparents']:
                    # 需要有祖父节点，且要求有足够多的祖父节点
                    grandparents = get_grandparents(self.dag, node)
                    if len(grandparents) < 1:
                        node_rejection_reasons.append(f"祖父节点类型需求不满足: {len(grandparents)} < 1")
                        rejection_reasons[node] = node_rejection_reasons.copy()
                        continue
                elif node_type in ['single_sibling', 'all_siblings', 'all_non_siblings']:
                    # 需要有至少3个兄弟节点
                    siblings = get_siblings(self.dag, node)
                    if len(siblings) < 1:
                        node_rejection_reasons.append(f"兄弟节点类型需求不满足: {len(siblings)} < 1")
                        rejection_reasons[node] = node_rejection_reasons.copy()
                        continue

                # 如果通过了所有条件检查，则为有效目标节点
                layer_valid_targets.append(node)

                # 如果节点被拒绝，记录拒绝原因
                if node_rejection_reasons:
                    rejection_reasons[node] = node_rejection_reasons
                    print(f"节点 {node} 被拒绝，原因是: {node_rejection_reasons}")
            # 如果当前层找到了有效目标节点，就不再搜索上一层
            if layer_valid_targets:
                # 对有效目标节点进行排序以确保确定性的顺序
                layer_valid_targets.sort()
                # 可以加权选择更稠密的节点
                weights = []
                for node in layer_valid_targets:
                    parents = get_parents(self.dag, node)
                    children = get_children(self.dag, node)
                    spouses = get_spouses(self.dag, node)
                    density = len(parents) + len(children) + len(spouses)
                    weights.append(density)

                # 归一化权重
                if sum(weights) > 0:
                    weights = np.array(weights) / sum(weights)
                else:  # All weights are 0, choose uniformly
                    weights = np.ones(len(layer_valid_targets)) / len(layer_valid_targets)

                # 根据权重选择节点，局部更稠密的节点有更高的选择概率
                # 使用种子上下文管理器确保目标节点选择的可重现性
                with SeedManager.seed_context(self.seed, 9000):
                    self.selected_target = np.random.choice(layer_valid_targets, p=weights)

                print(f"在层 {layer_idx} 中成功选择目标节点: {self.selected_target}")
                return
            else:
                print(f"在层 {layer_idx} 中未找到有效目标节点，将尝试上一层")

        # 如果所有层都搜索完了依然没有找到有效目标节点，直接跳过当前数据集
        print(f"在所有可搜索层中未找到类型 {node_type} 的有效目标节点，跳过当前数据集生成")

        # 打印详细的拒绝原因
        print("节点拒绝原因:")
        for node, reasons in rejection_reasons.items():
            print(f"  节点 {node}: {', '.join(reasons)}")

        # 如果没有拒绝原因记录，可能是因为没有节点被检查
        if not rejection_reasons:
            print("  没有节点被检查，可能是因为指定层没有节点。")
        else:
            print(f"  共检查了 {len(rejection_reasons)} 个节点，所有节点都被拒绝")

        # 打印DAG的基本信息，帮助调试
        print(f"DAG信息: 总节点数={len(self.dag.nodes())}, 总边数={len(self.dag.edges())}, 层数={len(self.layer_nodes)}")
        print(f"各层节点数量: {[len(layer) for layer in self.layer_nodes]}")

        if self.allow_skip:
            raise SkipDatasetException(f"未能找到类型 '{node_type}' 的有效目标节点")
        else:
            raise ValueError(f"未能找到类型 '{node_type}' 的有效目标节点")

    # 定义 SCM 类的前向传播方法
    def forward(self, exist_boudries=None, target_node=None, selected_features=None):  # exist_boudries 参数用于在后续调用时复用类别边界
        # 如果外部传入了目标节点和特征节点，则使用它们
        if target_node is not None and selected_features is not None:
            self.selected_target = target_node
            self.selected_features = selected_features
            print("---------------------SCM.forward 使用外部传入的目标节点是：--------------------")
            print(self.selected_target)
            print("---------------------SCM.forward 使用外部传入的特征节点是：--------------------")
            print(self.selected_features)
        # 如果没有外部传入，但是预定义结构（非随机图），则使用预定义的目标节点和特征
        elif hasattr(self, 'is_predefined_structure') and self.is_predefined_structure:
            print("---------------------SCM.forward 使用预定义结构的目标节点是：--------------------")
            print(self.selected_target)
            print("---------------------SCM.forward 使用预定义结构的特征节点是：--------------------")
            print(self.selected_features)
        # 否则（随机图结构），动态选择目标节点和特征
        else:
            self.select_valid_target_node()
            print("---------------------SCM.forward 内部选择的目标节点是：--------------------")
            print(self.selected_target)
            # 重新选择特征，确保目标节点不在其中
            self.selected_features = self.select_features()  # Pass self.selected_target implicitly
            print("---------------------SCM.forward 内部选择的特征节点是：--------------------")
            print(self.selected_features)

        # 使用统一的原始数据生成方法
        original_data, noise_params_cache, assignment_functions, _ = self._generate_original_data(seed_offset=1000)

        # 保存原始数据到self.data，以便后续统计分析
        self.data = original_data.copy()

        # Select corresponding data
        values = torch.cat([self.data[node] for node in self.dag.nodes], dim=1)
        node_to_idx = {node: i for i, node in enumerate(self.dag.nodes())}  # node to index mapping
        selected_target_idx = node_to_idx[self.selected_target]
        selected_feature_indices = [node_to_idx[feat_node] for feat_node in self.selected_features if
                                    feat_node in node_to_idx]

        y = values[:, [selected_target_idx]]
        x = values[:, selected_feature_indices]

        # 生成类别目标 (如果任务是分类)
        class_boundaries = None  # 初始化类别边界
        if self.task == 'classification':
            # 如果任务是分类
            if exist_boudries is not None:
                # 如果提供了已存在的类别边界，则使用它们
                y, class_boundaries = self.class_assigner(y, exist_boudries)
            else:
                # 否则，新生成类别边界
                y, class_boundaries = self.class_assigner(y)
            # 将目标变量 y 转换为整数类型并移除多余的维度
            y = y.int().squeeze()

        elif self.task == 'regression':
            # 如果任务是回归，将 y 转换为浮点型并移除多余维度
            y = y.float().squeeze()

        # 生成SNR验证结果（对所有方法都进行，包括非SNR方法）
        # 只在第一次原始数据生成时进行SNR验证
        if not hasattr(self, 'snr_validation_completed'):
            self._generate_snr_validation_results()
            self.snr_validation_completed = True  # 标记验证已完成

        # 返回特征 x，目标 y，以及类别边界 (如果适用)
        return x, y, class_boundaries

    def get_intervention_nodes(self, intervention_node_type):
        """根据干预节点类型获取要干预的节点集合"""

        # 优先使用预定义的干预节点（只有当不为None时才使用）
        if (hasattr(self, 'predefined_intervention_nodes') and
            self.predefined_intervention_nodes is not None and
            len(self.predefined_intervention_nodes) > 0):
            print(f"使用预定义的干预节点: {self.predefined_intervention_nodes}")
            return set(self.predefined_intervention_nodes)

        # 使用NodeSelector统一处理节点选择
        return NodeSelector.select_nodes_by_type(
            self.dag, self.selected_target, intervention_node_type, self.seed, 100
        )

    def get_unobserved_nodes(self, node_unobserved, intervention_nodes, target_node, custom_unobserved_nodes=None):
        """
        确定哪些节点不可被观测
        """
        # 优先使用预定义的不可观测节点（只有当不为None且非空时才使用）
        if (hasattr(self, 'predefined_unobserved_nodes') and
            self.predefined_unobserved_nodes is not None and
            len(self.predefined_unobserved_nodes) > 0):
            print(f"使用预定义的不可观测节点: {self.predefined_unobserved_nodes}")
            if isinstance(self.predefined_unobserved_nodes, (list, tuple)):
                return set(self.predefined_unobserved_nodes)
            elif isinstance(self.predefined_unobserved_nodes, set):
                return self.predefined_unobserved_nodes
            else:
                return {self.predefined_unobserved_nodes}  # 单个节点转换为集合

        # 使用NodeSelector统一处理不可观测节点选择
        return NodeSelector.select_unobserved_nodes(
            self.dag, target_node, node_unobserved, intervention_nodes,
            custom_unobserved_nodes, self.seed
        )

    def generate_sample_index(self, original_data_for_stats, node):
        """
        为多个干预节点生成一个共享的样本索引，确保所有节点从同一个原始数据样本中采样。
        """
        if original_data_for_stats is None or node not in original_data_for_stats or original_data_for_stats[node].numel() == 0:
            return None

        num_train_samples = original_data_for_stats[node].shape[0]
        if num_train_samples == 0:
            return None

        # 使用种子上下文管理器确保可重现性
        with SeedManager.seed_context(self.seed, 6000):
            return np.random.randint(0, num_train_samples)

    def generate_intervention_value(self, node, intervention_value_method, original_data_for_stats=None, sample_idx=None):
        """
        根据指定的方法生成干预值.
        original_data_for_stats: 用于计算统计量（如均值、采样、随机范围）的数据，应为训练数据部分。
        sample_idx: 可选的样本索引，用于'sample'方法，确保多个节点从同一个样本中采样
        """
        # 生成的干预值将应用于所有 self.num_samples 个样本
        output_shape = (self.num_samples, 1)

        # 获取节点种子索引，用于确定性随机种子
        if node not in self.node_seed_indices:
            raise ValueError(f"节点 {node} 不在节点种子索引映射中。这可能是因为节点名称错误或图结构发生了变化。")
        new_node_idx = self.node_seed_indices[node]

        if intervention_value_method == 'mean':
            # (1) 原始数据该节点的值的均值 (基于 original_data_for_stats)
            if original_data_for_stats is None or node not in original_data_for_stats or original_data_for_stats[
                node].numel() == 0:
                print(
                    f"警告: generate_intervention_value 'mean' 方法缺少 original_data_for_stats[{node}]。使用全局随机值。")
                return uniform_sampler_f(0, 1)() * torch.ones(output_shape, device=self.device)

            mean_val = original_data_for_stats[node].mean().item()
            return mean_val * torch.ones(output_shape, device=self.device)

        elif intervention_value_method == 'sample':
            # (2) 原始数据该节点的值中采样的一个值 (基于 original_data_for_stats)
            if original_data_for_stats is None or node not in original_data_for_stats or original_data_for_stats[
                node].numel() == 0:
                print(
                    f"警告: generate_intervention_value 'sample' 方法缺少 original_data_for_stats[{node}]。使用全局随机值。")
                return uniform_sampler_f(0, 1)() * torch.ones(output_shape, device=self.device)

            num_train_samples = original_data_for_stats[node].shape[0]
            if num_train_samples == 0:  # Should be caught by numel check above, but defensive
                print(
                    f"警告: generate_intervention_value 'sample' 方法的 original_data_for_stats[{node}] 为空。使用全局随机值。")
                return uniform_sampler_f(0, 1)() * torch.ones(output_shape, device=self.device)

            # 如果提供了样本索引，使用它；否则生成一个新的
            if sample_idx is not None:
                # 确保索引在有效范围内
                if sample_idx >= 0 and sample_idx < num_train_samples:
                    sampled_val = original_data_for_stats[node][sample_idx].item()
                else:
                    print(f"警告: 提供的样本索引 {sample_idx} 超出范围 [0, {num_train_samples-1}]。生成新的随机索引。")
                    # 使用种子上下文管理器确保可重现性
                    with SeedManager.seed_context(self.seed, 6100 + new_node_idx % 1000):
                        sample_idx = np.random.randint(0, num_train_samples)
                        sampled_val = original_data_for_stats[node][sample_idx].item()
            else:
                # 使用种子上下文管理器确保可重现性
                with SeedManager.seed_context(self.seed, 6200 + new_node_idx % 1000):
                    sample_idx = np.random.randint(0, num_train_samples)
                    sampled_val = original_data_for_stats[node][sample_idx].item()

            return sampled_val * torch.ones(output_shape, device=self.device)

        elif intervention_value_method == 'random':
            # (3) 随机设定一个不在原始数据该节点值范围中的值 (基于 original_data_for_stats 的范围)
            if original_data_for_stats is not None and node in original_data_for_stats and original_data_for_stats[
                node].numel() > 0:
                min_val = original_data_for_stats[node].min().item()
                max_val = original_data_for_stats[node].max().item()
                # 生成一个超出范围的值
                # 使用种子上下文管理器确保可重现性
                with SeedManager.seed_context(self.seed, 5000 + new_node_idx % 1000):
                    if random.random() > 0.5:
                        # 大于最大值
                        seed_for_uniform = self.seed + 5100 + new_node_idx % 1000 if self.seed is not None else None
                        return (max_val + uniform_sampler_f(0.1, 1.0, seed_for_uniform)()) * torch.ones(output_shape, device=self.device)
                    else:
                        # 小于最小值
                        seed_for_uniform = self.seed + 5100 + new_node_idx % 1000 if self.seed is not None else None
                        return (min_val - uniform_sampler_f(0.1, 1.0, seed_for_uniform)()) * torch.ones(output_shape, device=self.device)
            else:
                # 如果没有原始数据或数据为空，就生成一个全局随机值
                print(
                    f"警告: generate_intervention_value 'random' 方法缺少或 original_data_for_stats[{node}] 为空。使用全局随机值。")
                seed_for_uniform = self.seed + 5200 + new_node_idx % 1000 if self.seed is not None else None
                return uniform_sampler_f(0, 1, seed_for_uniform)() * torch.ones(output_shape, device=self.device)
        else:
            # 默认使用随机值
            print(f"警告: 未知的 intervention_value_method '{intervention_value_method}'。使用全局随机值。")
            seed_for_uniform = self.seed + 5300 + new_node_idx % 1000 if self.seed is not None else None
            return uniform_sampler_f(0, 1, seed_for_uniform)() * torch.ones(output_shape, device=self.device)

    def _generate_intervention_data(self, operation_type, node_type, value_method='sample',
                                   node_unobserved=False, unobserved_nodes=None,
                                   custom_nodes=None, sort_nodes=True,
                                   predefined_nodes_attr=None, operation_name="intervention"):
        """
        统一的干预/扰动数据生成方法

        参数:
            operation_type: 操作类型，'counterfactual' 或 'statistical'
            node_type: 节点类型，例如 'single_parent', 'all_parents' 等
            value_method: 值的生成方法，'mean', 'sample', 或 'random'
            node_unobserved: 控制不可观测节点的选择方式
            unobserved_nodes: 直接指定不可被观测的节点列表或集合
            custom_nodes: 自定义节点集合
            sort_nodes: 是否对节点进行排序
            predefined_nodes_attr: 预定义节点属性名
            operation_name: 操作名称（用于日志）

        返回:
            (原始数据, 处理后数据)，每个都是 (x, y) 元组
        """
        # 使用初始化时已经选择好的目标节点和特征节点
        current_target_node = self.selected_target
        current_selected_features = self.selected_features

        # 获取要处理的节点
        if custom_nodes is not None:
            nodes = custom_nodes
            print(f"使用自定义{operation_name}节点: {nodes}")
        elif (predefined_nodes_attr and hasattr(self, predefined_nodes_attr) and
              getattr(self, predefined_nodes_attr) is not None and
              len(getattr(self, predefined_nodes_attr)) > 0):
            predefined_nodes = getattr(self, predefined_nodes_attr)
            nodes = set(predefined_nodes)
            print(f"使用预定义的{operation_name}节点: {predefined_nodes}")
        else:
            nodes = self.get_intervention_nodes(node_type)

        # 如果需要排序，将集合转换为排序后的列表
        if sort_nodes and isinstance(nodes, set):
            nodes = sorted(list(nodes))

        # 检查是否找到节点
        if not nodes:
            print(f"警告: 未找到合适的{operation_name}节点。对于目标 {current_target_node} 和类型 {node_type}")

        # 保存节点到对象属性
        if operation_name == "intervention":
            self.intervention_nodes = nodes
        else:
            self.perturbation_nodes = nodes

        # 使用统一的原始数据生成方法
        seed_offset = 2000 if operation_name == "perturbation" else 3000
        original_data, noise_params_cache, assignment_functions, original_data_for_stats_calc = self._generate_original_data(seed_offset=seed_offset)

        # 确定不可观测的节点
        nodes_to_hide = self.get_unobserved_nodes(node_unobserved, nodes, current_target_node, unobserved_nodes)
        # 保存不可观测节点到对象属性中，以便后续使用
        self.unobserved_nodes = nodes_to_hide

        # 如果使用'sample'方法，为所有节点生成一个共享的样本索引
        shared_sample_idx = None
        if value_method == 'sample' and nodes:
            # 选择第一个节点来生成样本索引
            if isinstance(nodes, list) and nodes:
                first_node = nodes[0]
            else:
                first_node = next(iter(nodes)) if nodes else None

            if first_node is not None:
                shared_sample_idx = self.generate_sample_index(original_data_for_stats_calc, first_node)
                if shared_sample_idx is not None:
                    print(f"为所有{operation_name}节点使用共享样本索引: {shared_sample_idx}")
                else:
                    print(f"警告: 无法为{operation_name}节点生成有效的共享样本索引，每个节点将使用独立的随机索引")

        # 生成处理后的数据
        if operation_name == "intervention":
            # 干预：需要创建干预后的DAG（移除干预节点的父边）
            processed_data = self._generate_intervened_data(
                original_data, nodes, value_method, original_data_for_stats_calc,
                shared_sample_idx, assignment_functions
            )
        else:
            # 扰动：直接替换节点值，不修改图结构
            processed_data = self._generate_perturbed_data(
                original_data, nodes, value_method, original_data_for_stats_calc, shared_sample_idx
            )

        # 收集和处理数据
        (x_original_full, y_original_full, x_processed_full, y_processed_full,
         selected_target_idx, selected_feature_indices) = DataProcessor.collect_and_process_data(
            original_data=original_data,
            processed_data=processed_data,
            dag=self.dag,
            current_target_node=current_target_node,
            current_selected_features=current_selected_features,
            nodes_to_hide=nodes_to_hide
        )

        # 处理分类任务
        y_original_full, y_processed_full, class_boundaries_for_all = DataProcessor.handle_classification_task(
            y_original_full, y_processed_full, self.task, self.class_assigner if hasattr(self, 'class_assigner') else None
        )

        # 保存原始数据到self.data，以便后续统计分析
        self.data = original_data.copy()

        # 生成SNR验证结果（对所有方法都进行，包括非SNR方法）
        if not hasattr(self, 'snr_validation_completed'):
            self._generate_snr_validation_results()
            self.snr_validation_completed = True  # 标记验证已完成

        # 根据操作类型返回数据
        output_original, output_processed = DataProcessor.generate_final_output(
            operation_type=operation_type,
            x_original_full=x_original_full,
            y_original_full=y_original_full,
            x_processed_full=x_processed_full,
            y_processed_full=y_processed_full,
            class_boundaries_for_all=class_boundaries_for_all,
            current_target_node=current_target_node,
            current_selected_features=current_selected_features,
            num_samples=self.num_samples,
            train_test_split_ratio=self.train_test_split_ratio,
            forward_func=self.forward
        )

        return output_original, output_processed

    def _generate_intervened_data(self, original_data, intervention_nodes, value_method,
                                 original_data_for_stats_calc, shared_sample_idx, assignment_functions):
        """生成干预后的数据"""
        import copy

        # 创建干预DAG（移除干预节点的父节点边）
        intervened_dag = copy.deepcopy(self.dag)

        for node in intervention_nodes:
            parent_intervention = list(self.dag.predecessors(node))
            intervened_dag.remove_edges_from([(parent, node) for parent in parent_intervention])

        # 获取干预节点在干预后图中的所有后代
        descendants = set()
        for node in intervention_nodes:
            descendants |= get_all_descendants(intervened_dag, node)

        # 传播干预数据
        intervened_data = {}
        # 首先处理干预后图中的所有节点
        for node in nx.topological_sort(intervened_dag):
            if node in self.root_nodes:
                if node in intervention_nodes:
                    # 为根节点干预生成干预值，使用共享样本索引
                    intervened_data[node] = self.generate_intervention_value(node, value_method,
                                                                             original_data_for_stats_calc,
                                                                             sample_idx=shared_sample_idx)
                else:
                    # 非干预根节点保持原值
                    intervened_data[node] = original_data[node]
            else:
                if node in intervention_nodes:
                    # 为非根节点干预生成干预值，使用共享样本索引
                    intervened_data[node] = self.generate_intervention_value(node, value_method,
                                                                             original_data_for_stats_calc,
                                                                             sample_idx=shared_sample_idx)
                else:
                    # 检查节点是否是干预节点的后代
                    if node in descendants:
                        # 如果是后代，使用干预后的父节点值计算
                        intervened_parents = list(intervened_dag.predecessors(node))
                        if intervened_parents:
                            parents_data_intervention = torch.cat(
                                [intervened_data[parent] for parent in intervened_parents], dim=1)
                            # 使用与原始数据生成时相同的赋值函数，确保函数和噪声参数一致
                            if node in assignment_functions:
                                intervened_data[node] = assignment_functions[node](parents_data_intervention)
                            else:
                                intervened_data[node] = self.assignment[node]['assignment'](parents_data_intervention)
                        else:
                            # 理论上不会进入这里
                            raise RuntimeError(f"节点{node}在干预图中没有父节点，但它不是根节点也不是干预节点，这种情况不应出现。")
                    else:
                        # 如果不是后代，保持原值
                        intervened_data[node] = original_data[node]

        # 确保所有原始图中的节点都在intervened_data中
        for node in self.dag.nodes():
            if node not in intervened_data:
                intervened_data[node] = original_data[node]

        return intervened_data

    def _generate_perturbed_data(self, original_data, perturbation_nodes, value_method,
                                original_data_for_stats_calc, shared_sample_idx):
        """生成扰动后的数据"""
        # 传播扰动数据 - 与原始数据相同，除了扰动节点
        perturbed_data = {}
        for node in nx.topological_sort(self.dag):
            if node in perturbation_nodes:
                # 为扰动节点生成扰动值，使用与干预函数相同的策略，并使用共享样本索引
                perturbed_data[node] = self.generate_intervention_value(node, value_method,
                                                                        original_data_for_stats_calc,
                                                                        sample_idx=shared_sample_idx)
            else:
                # 非扰动节点保持原值
                perturbed_data[node] = original_data[node]

        return perturbed_data

    def perturbation(self, perturbation_type, perturbation_node_type, perturbation_value_method='sample',
                     node_unobserved=False, unobserved_nodes=None, sort_perturbation_nodes=True):
        """
        生成扰动数据，使用统一的干预/扰动数据生成逻辑

        参数:
            perturbation_type: 扰动类型，'counterfactual' 或 'statistical'
            perturbation_node_type: 扰动节点类型，例如 'single_parent', 'all_parents' 等
            perturbation_value_method: 扰动值的生成方法，'mean', 'sample', 或 'random'
            node_unobserved: 控制不可观测节点的选择方式
            unobserved_nodes: 直接指定不可被观测的节点列表或集合
            sort_perturbation_nodes: 是否对扰动节点进行排序，确保在不同运行中顺序一致

        返回:
            (原始数据, 扰动数据)，每个都是 (x, y) 元组
        """
        return self._generate_intervention_data(
            operation_type=perturbation_type,
            node_type=perturbation_node_type,
            value_method=perturbation_value_method,
            node_unobserved=node_unobserved,
            unobserved_nodes=unobserved_nodes,
            custom_nodes=None,
            sort_nodes=sort_perturbation_nodes,
            predefined_nodes_attr='predefined_perturbation_nodes',
            operation_name="perturbation"
        )

    def intervention(self, intervention_type, intervention_node_type=None,
                    intervention_value_method='sample', node_unobserved=False,
                    unobserved_nodes=None, custom_intervention_nodes=None,
                    sort_intervention_nodes=True):
        """
        生成干预数据，使用统一的干预/扰动数据生成逻辑

        参数:
            intervention_type: 干预类型，'counterfactual' 或 'statistical'
            intervention_node_type: 干预节点类型，例如 'single_parent', 'all_parents' 等
            intervention_value_method: 干预值的生成方法，'mean', 'sample', 或 'random'
            node_unobserved: 控制不可观测节点的选择方式
            unobserved_nodes: 直接指定不可被观测的节点列表或集合
            custom_intervention_nodes: 可选的自定义干预节点集合
            sort_intervention_nodes: 是否对干预节点进行排序

        返回:
            (原始数据, 干预数据)，每个都是 (x, y) 元组
        """
        return self._generate_intervention_data(
            operation_type=intervention_type,
            node_type=intervention_node_type,
            value_method=intervention_value_method,
            node_unobserved=node_unobserved,
            unobserved_nodes=unobserved_nodes,
            custom_nodes=custom_intervention_nodes,
            sort_nodes=sort_intervention_nodes,
            predefined_nodes_attr='predefined_intervention_nodes',
            operation_name="intervention"
        )

    def _generate_original_data(self, seed_offset=0):
        """
        简化的原始数据生成方法，保留核心逻辑但减少重复代码

        注意：这里只实现了基本的数据生成逻辑，完整的蒙特卡洛计算和SNR处理
        可以在后续版本中添加，目前为了简化代码结构
        """
        # 恢复原始种子设置
        if self.seed is not None:
            torch.manual_seed(self.seed + seed_offset)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(self.seed + seed_offset)

        # 根节点采样
        root_data_base = self._sample_root_data(self.num_samples, len(self.root_nodes), self.device)

        # 创建一个从根节点到其在 root_data_base 中列索引的映射
        root_node_to_idx_map = {node: i for i, node in enumerate(self.root_nodes)}

        # 初始化SNR验证相关属性
        self.snr_validation_results = None
        self._snr_collection = {
            'target_snr': {},           # 目标SNR（仅对设定了目标SNR的节点）
            'configured_noise_std': {}, # 配置的噪声标准差
            'signal_var': {},           # 实际信号方差
            'signal_mean': {},          # 实际信号均值
            'actual_noise_var': {},     # 实际噪声方差
            'actual_noise_mean': {},    # 实际噪声均值
            'configured_noise_mean': {},    # 配置的噪声均值
            'calculated_noise_mean_target': {}  # 基于信号均值计算的噪声均值
        }

        # 初始化数据存储
        original_data = {}
        assignment_functions = {}  # 存储赋值函数，用于干预数据生成

        # 实际数据生成（按拓扑顺序）
        for node in nx.topological_sort(self.dag):
            if node in self.root_nodes:
                # 根节点无需噪声计算，直接使用实际采样数据
                original_data[node] = root_data_base[:, root_node_to_idx_map[node]].unsqueeze(1)
            else:
                # 直接获取实际数据生成时的父亲节点数据
                parents_data = torch.cat([original_data[parent] for parent in self.assignment[node]['parents']],
                                         dim=1)

                assignment_func = self.assignment[node]['assignment']

                # 通过f函数计算信号
                with torch.no_grad():
                    signal_data = assignment_func.f_function(parents_data)
                    if hasattr(assignment_func, 'g_function') and assignment_func.g_function is not None:
                        signal_data = assignment_func.g_function(signal_data)

                # 计算实际信号方差和均值
                signal_var = torch.var(signal_data).item()
                signal_mean = torch.mean(signal_data).item()

                # 简化的SNR处理逻辑
                original_noise_std = assignment_func.noise_std

                # 检查是否有自定义函数的目标SNR
                custom_target_snr = None
                if hasattr(assignment_func, 'target_snr'):
                    custom_target_snr = assignment_func.target_snr

                if custom_target_snr is not None:
                    # 基于目标SNR和实际信号方差计算噪声方差
                    calculated_noise_variance = signal_var / custom_target_snr
                    noise_std = np.sqrt(calculated_noise_variance)
                    print(f"节点{node} 动态计算噪声标准差: {noise_std:.6f}")

                    # 更新噪声标准差
                    assignment_func.noise_std = noise_std

                    # 收集SNR信息用于后续验证
                    if hasattr(self, '_snr_collection'):
                        self._snr_collection['target_snr'][node] = custom_target_snr
                        self._snr_collection['configured_noise_std'][node] = noise_std
                        self._snr_collection['signal_var'][node] = signal_var
                        self._snr_collection['signal_mean'][node] = signal_mean
                else:
                    # 不使用SNR方法：保持原始噪声标准差
                    if hasattr(self, '_snr_collection'):
                        self._snr_collection['signal_var'][node] = signal_var
                        self._snr_collection['signal_mean'][node] = signal_mean
                        self._snr_collection['configured_noise_std'][node] = original_noise_std

                # 保存赋值函数用于干预数据生成
                assignment_functions[node] = assignment_func

                # 信号+噪声得到当前节点的生成数据，作为下一个子节点的实际父亲数据
                final_data = assignment_func(parents_data)
                original_data[node] = final_data

                # 计算实际噪声方差和均值用于验证
                if hasattr(self, '_snr_collection'):
                    try:
                        # 使用实际采样的噪声样本计算统计量
                        if hasattr(assignment_func, 'last_noise_sample') and assignment_func.last_noise_sample is not None:
                            noise_sample = assignment_func.last_noise_sample
                            actual_noise_var = torch.var(noise_sample).item()
                            actual_noise_mean = torch.mean(noise_sample).item()
                            self._snr_collection['actual_noise_var'][node] = actual_noise_var
                            self._snr_collection['actual_noise_mean'][node] = actual_noise_mean
                        else:
                            # 如果没有噪声样本，回退到原来的计算方式（但这不准确）
                            noise_part = final_data - signal_data
                            actual_noise_var = torch.var(noise_part).item()
                            actual_noise_mean = torch.mean(noise_part).item()
                            self._snr_collection['actual_noise_var'][node] = actual_noise_var
                            self._snr_collection['actual_noise_mean'][node] = actual_noise_mean
                            print(f"警告：节点{node}没有找到噪声样本，使用不准确的计算方式")
                    except Exception as e:
                        print(f"计算节点{node}实际噪声统计时出错: {e}")

        # 计算训练数据子集，用于统计计算
        train_data_end_idx = int(self.num_samples * self.train_test_split_ratio)
        original_data_for_stats_calc = {
            node_key: data_val[:train_data_end_idx]
            for node_key, data_val in original_data.items()
        }

        return original_data, {}, assignment_functions, original_data_for_stats_calc

    def _generate_snr_validation_results(self):
        """
        基于收集的SNR信息生成验证结果
        """
        if not hasattr(self, '_snr_collection'):
            return

        try:
            target_snr = self._snr_collection.get('target_snr', {})
            configured_noise_std = self._snr_collection.get('configured_noise_std', {})
            signal_var = self._snr_collection.get('signal_var', {})
            signal_mean = self._snr_collection.get('signal_mean', {})
            actual_noise_var = self._snr_collection.get('actual_noise_var', {})
            actual_noise_mean = self._snr_collection.get('actual_noise_mean', {})
            configured_noise_mean = self._snr_collection.get('configured_noise_mean', {})
            calculated_noise_mean_target = self._snr_collection.get('calculated_noise_mean_target', {})

            # 为所有节点生成完整的SNR信息
            all_nodes = set(signal_var.keys()) | set(actual_noise_var.keys()) | set(configured_noise_std.keys())

            # 初始化结果字典
            result_target_snr = {}
            result_configured_noise_std = {}
            result_actual_noise_std = {}
            result_actual_snr = {}

            for node in all_nodes:
                # 1. 目标SNR
                result_target_snr[node] = target_snr.get(node, None)

                # 2. 配置的噪声标准差
                result_configured_noise_std[node] = configured_noise_std.get(node, None)

                # 3. 实际噪声标准差
                if node in actual_noise_var:
                    actual_noise_variance = actual_noise_var[node]
                    result_actual_noise_std[node] = np.sqrt(actual_noise_variance) if actual_noise_variance >= 0 else None
                else:
                    result_actual_noise_std[node] = None

                # 4. 实际SNR
                if node in signal_var and node in actual_noise_var:
                    sig_var = signal_var[node]
                    actual_noi_var = actual_noise_var[node]
                    if actual_noi_var > 0:
                        result_actual_snr[node] = sig_var / actual_noi_var
                    else:
                        result_actual_snr[node] = None
                else:
                    result_actual_snr[node] = None

            # 生成验证结果
            self.snr_validation_results = {
                'target_snr': result_target_snr,
                'configured_noise_std': result_configured_noise_std,
                'actual_noise_std': result_actual_noise_std,
                'actual_snr': result_actual_snr,
                'signal_var': signal_var,  # 保留信号方差用于调试
                'signal_mean': signal_mean,  # 保留信号均值用于调试
                'actual_noise_var': actual_noise_var,  # 保留噪声方差用于调试
                'actual_noise_mean': actual_noise_mean,  # 实际噪声均值
                'config_noise_mean': configured_noise_mean,  # 配置的噪声均值
                'calculated_noise_mean_target': calculated_noise_mean_target  # 基于信号均值计算的噪声均值
            }

            self.target_snr_by_node = result_target_snr
            self.actual_snr_by_node = result_actual_snr

            # 打印简要结果
            snr_nodes = [node for node in result_target_snr.keys() if result_target_snr[node] is not None]
            non_snr_nodes = [node for node in result_target_snr.keys() if result_target_snr[node] is None]

            if snr_nodes:
                print(f"SNR方法节点数: {len(snr_nodes)}")
            if non_snr_nodes:
                print(f"固定噪声标准差节点数: {len(non_snr_nodes)}")

            all_actual_snr = [snr for snr in result_actual_snr.values() if snr is not None]
            if all_actual_snr:
                mean_actual_snr = np.mean(all_actual_snr)
                print(f"平均实际SNR: {mean_actual_snr:.4f}, 总节点数: {len(all_actual_snr)}")

        except Exception as e:
            print(f"生成SNR验证结果时出错: {e}")
            self.snr_validation_results = None

    def get_snr_validation_report(self):
        """
        获取详细的SNR验证报告
        """
        if not hasattr(self, 'snr_validation_results') or self.snr_validation_results is None:
            return "未进行SNR验证或验证失败"

        results = self.snr_validation_results
        report = []

        report.append("=" * 60)
        report.append("SNR验证报告")
        report.append("=" * 60)
        report.append("验证原因: 噪声样本的实际方差与理论计算可能有差异")
        report.append("")

        # 节点级别详细信息
        if 'actual_snr' in results:
            actual_snr = results['actual_snr']
            target_snr = results.get('target_snr', {})
            signal_var = results.get('signal_var', {})
            actual_noise_var = results.get('actual_noise_var', {})

            report.append("\n节点级别详细信息:")
            report.append("-" * 100)

            if target_snr:
                # SNR方法：显示目标SNR和实际SNR的对比
                report.append(f"{'节点':<8} {'目标SNR':<10} {'实际SNR':<10} {'信号方差':<12} {'实际噪声方差':<12}")
                report.append("-" * 100)

                for node in sorted(target_snr.keys()):
                    if node in actual_snr:
                        target = target_snr[node]
                        actual = actual_snr[node]
                        sig_var = signal_var.get(node, 0)
                        actual_noi_var = actual_noise_var.get(node, 0)

                        report.append(f"{str(node):<8} {target:<10.4f} {actual:<10.4f} {sig_var:<12.6f} {actual_noi_var:<12.6f}")
                        report.append("")
            else:
                # 非SNR方法：只显示实际SNR
                report.append(f"{'节点':<8} {'实际SNR':<10} {'信号方差':<12} {'实际噪声方差':<12}")
                report.append("-" * 100)

                for node in sorted(actual_snr.keys()):
                    actual = actual_snr[node]
                    sig_var = signal_var.get(node, 0)
                    actual_noi_var = actual_noise_var.get(node, 0)

                    report.append(f"{str(node):<8} {actual:<10.4f} {sig_var:<12.6f} {actual_noi_var:<12.6f}")
                    report.append("")

        report.append("=" * 60)
        return "\n".join(report)
